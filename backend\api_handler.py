# backend/api_handler.py
"""
核心API处理模块
主要负责应用初始化和API蓝图注册
元数据获取、链接验证等功能已迁移到独立的API模块中
"""
from flask import Blueprint

# 导入API模块
from api.settings_api import settings_api
from api.performance_api import performance_api
from api.media_api import media_api
from api.file_api import file_api
from api.log_api import log_api
from api.content_api import content_api
from api.image_api import image_api
from api.nfo_api import nfo_api
from api.metadata_api import metadata_api
from api.batch_import_api import batch_import_api
from api.cid_api import cid_api

# 创建API蓝图（保留用于向后兼容）
api = Blueprint('api', __name__)

# ==================== 所有功能已迁移到独立的API模块 ====================
# ==================== 应用初始化 ====================

def init_app(app):
    """注册所有API蓝图到Flask应用"""
    # 注册核心API（元数据获取等复杂业务逻辑）
    app.register_blueprint(api, url_prefix='/api')

    # 注册独立功能模块API
    app.register_blueprint(settings_api, url_prefix='/api')      # 设置管理
    app.register_blueprint(performance_api, url_prefix='/api')   # 性能监控
    app.register_blueprint(media_api, url_prefix='/api')         # 媒体文件服务
    app.register_blueprint(file_api, url_prefix='/api')          # 文件操作
    app.register_blueprint(log_api, url_prefix='/api')           # 日志管理
    app.register_blueprint(content_api, url_prefix='/api')       # 内容数据查询
    app.register_blueprint(image_api, url_prefix='/api')         # 图片处理
    app.register_blueprint(nfo_api, url_prefix='/api')           # NFO管理
    app.register_blueprint(metadata_api, url_prefix='/api')      # 元数据获取
    app.register_blueprint(batch_import_api, url_prefix='/api')  # 批量导入
    app.register_blueprint(cid_api, url_prefix='/api')           # CID规则管理
