#!/usr/bin/env python3
"""
CID表修复脚本
用于检查和创建缺失的CID相关数据库表
"""

import sqlite3
import os
import sys

def fix_cid_tables():
    """修复CID相关表"""
    
    # 数据库路径
    db_path = os.path.join('db', 'media.db')
    
    if not os.path.exists(db_path):
        print(f"错误：数据库文件不存在: {db_path}")
        return False
    
    print(f"连接数据库: {os.path.abspath(db_path)}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 启用外键约束
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # 检查现有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [row[0] for row in cursor.fetchall()]
        print(f"现有表: {existing_tables}")
        
        # 检查并创建cid_conversion_rules表
        if 'cid_conversion_rules' not in existing_tables:
            print("创建cid_conversion_rules表...")
            cursor.execute('''CREATE TABLE cid_conversion_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                series_name TEXT NOT NULL UNIQUE,
                cid_prefix TEXT,
                series_name_in_cid TEXT,
                number_padding INTEGER NOT NULL,
                suffix TEXT,
                description TEXT,
                status TEXT DEFAULT '已确认',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )''')
            cursor.execute('CREATE INDEX idx_cid_series_name ON cid_conversion_rules(series_name);')
            print("✓ cid_conversion_rules表创建成功")
        else:
            print("✓ cid_conversion_rules表已存在")
            
        # 检查并创建cid_variant_rules表
        if 'cid_variant_rules' not in existing_tables:
            print("创建cid_variant_rules表...")
            cursor.execute('''CREATE TABLE cid_variant_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                main_rule_id INTEGER NOT NULL,
                maker TEXT,
                maker_enabled INTEGER DEFAULT 0,
                serial_number_range TEXT,
                cid_prefix TEXT,
                series_name_in_cid TEXT,
                number_padding INTEGER NOT NULL,
                suffix TEXT,
                priority INTEGER DEFAULT 0,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (main_rule_id) REFERENCES cid_conversion_rules (id) ON DELETE CASCADE
            )''')
            cursor.execute('CREATE INDEX idx_cid_variant_main_rule_id ON cid_variant_rules(main_rule_id);')
            cursor.execute('CREATE INDEX idx_cid_variant_maker ON cid_variant_rules(maker);')
            cursor.execute('CREATE INDEX idx_cid_variant_priority ON cid_variant_rules(priority);')
            print("✓ cid_variant_rules表创建成功")
        else:
            print("✓ cid_variant_rules表已存在")
        
        # 提交更改
        conn.commit()
        
        # 验证表结构
        print("\n验证表结构:")
        for table in ['cid_conversion_rules', 'cid_variant_rules']:
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            print(f"{table}表字段:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
        
        print("\n✓ CID表修复完成!")
        return True
        
    except Exception as e:
        print(f"错误: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    print("CID表修复脚本")
    print("=" * 50)
    
    success = fix_cid_tables()
    
    if success:
        print("\n修复成功！现在可以重启应用。")
        sys.exit(0)
    else:
        print("\n修复失败！请检查错误信息。")
        sys.exit(1)
