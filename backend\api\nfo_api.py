# backend/api/nfo_api.py
"""
NFO管理API模块
负责处理NFO文件的读取、编辑、保存等功能
包括手工修正模式和数据清洗模式
"""
import os
from flask import Blueprint, request, jsonify, current_app

from db_manager import get_db_connection
import image_processor
from nfo_parser import parse_nfo_file, save_nfo_file, extract_bangou_from_title
from api.media_api import is_safe_path

# 创建NFO API蓝图
nfo_api = Blueprint('nfo_api', __name__)

# ==================== NFO管理相关功能 ====================

@nfo_api.route('/manual/find-movie', methods=['GET'])
def find_movie_by_query():
    """查找电影"""
    query = request.args.get('q', '').strip()
    if not query: 
        return jsonify([])
    
    conn = get_db_connection()
    search_query = f"%{query}%"
    movies = conn.execute(
        "SELECT id, bangou, title, item_path FROM movies WHERE bangou LIKE ? OR item_path LIKE ? LIMIT 10", 
        (search_query, search_query)
    ).fetchall()
    conn.close()
    return jsonify([dict(row) for row in movies])

@nfo_api.route('/manual/movie-details/<int:movie_id>', methods=['GET'])
def get_movie_details(movie_id):
    """获取电影详情"""
    conn = get_db_connection()
    movie = conn.execute("SELECT * FROM movies WHERE id = ?", (movie_id,)).fetchone()
    if not movie: 
        conn.close()
        return jsonify({"error": "未找到电影"}), 404
    
    pictures = conn.execute("SELECT * FROM pictures WHERE movie_id = ?", (movie_id,)).fetchone()
    nfo_records = conn.execute("SELECT id, nfo_path FROM nfo_data WHERE movie_id = ?", (movie_id,)).fetchall()
    conn.close()
    
    return jsonify({
        "movie": dict(movie), 
        "pictures": dict(pictures) if pictures else {}, 
        "nfo_files": [dict(row) for row in nfo_records]
    })

@nfo_api.route('/manual/nfo-content/<int:nfo_id>', methods=['GET'])
def get_nfo_content(nfo_id):
    """获取数据清洗模式的NFO内容"""
    conn = get_db_connection()
    nfo_record = conn.execute("SELECT nfo_path FROM nfo_data WHERE id = ?", (nfo_id,)).fetchone()
    conn.close()
    
    if not nfo_record: 
        return jsonify({"error": "未找到NFO记录"}), 404
        
    nfo_path = nfo_record['nfo_path']
    if not is_safe_path(nfo_path): 
        return jsonify({"error": "无效的NFO路径"}), 400
        
    try:
        # 解析NFO文件
        nfo_data = parse_nfo_file(nfo_path)
        
        # 确保返回的是可序列化的数据
        if nfo_data and '_nfo_path' in nfo_data:
            nfo_data.pop('_nfo_path', None)
            
        if not nfo_data:
            return jsonify({"error": "NFO文件解析失败"}), 500
            
        return jsonify(nfo_data)
    except Exception as e: 
        current_app.logger.error(f"读取NFO文件失败: {e}", exc_info=True)
        return jsonify({"error": f"读取NFO文件失败: {e}"}), 500

@nfo_api.route('/manual/save-nfo/<int:nfo_id>', methods=['POST'])
def save_nfo_content(nfo_id):
    """数据清洗模式保存NFO文件，同时更新数据库"""
    data = request.json
    if not data:
        return jsonify({"success": False, "message": "请求数据为空"}), 400
        
    conn = get_db_connection()
    
    try:
        # 获取NFO记录
        nfo_record = conn.execute("SELECT nfo_path, strm_name FROM nfo_data WHERE id = ?", (nfo_id,)).fetchone()
        if not nfo_record:
            conn.close()
            return jsonify({"success": False, "message": "未找到NFO记录"}), 404
            
        nfo_path = nfo_record['nfo_path']
        # 修复: sqlite3.Row对象使用索引方式访问，不要用.get()方法
        # 如果strm_name不存在，使用空字符串作为默认值
        strm_name = nfo_record['strm_name'] if 'strm_name' in nfo_record.keys() else ''
        
        if not is_safe_path(nfo_path):
            conn.close()
            return jsonify({"success": False, "message": "无效的NFO路径"}), 400
            
        # 处理标题和原始标题，从数据库角度需要拼接番号，但在NFO中已由save_nfo_file处理
        
        # 保存到NFO文件，使用'database'模式，确保适当处理番号
        success, message = save_nfo_file(nfo_path, data, mode='database')
        if not success:
            conn.close()
            return jsonify({"success": False, "message": message}), 500
            
        # 处理数据库更新
        # 为数据库中的字段处理：提取标题中的番号并清理
        _, clean_title = extract_bangou_from_title(data.get('title', ''))
        if 'title' in data:
            data['title'] = clean_title
            
        # 同样处理originaltitle
        if 'originaltitle' in data:
            _, clean_orig_title = extract_bangou_from_title(data.get('originaltitle', ''))
            data['originaltitle'] = clean_orig_title
        
        # 更新数据库中的NFO记录
        nfo_main_cols = ['originaltitle', 'plot', 'originalplot', 'tagline', 'release_date', 'year', 'rating', 'criticrating']
        nfo_main_vals = [data.get(col) for col in nfo_main_cols]
        
        # 仅更新存在的字段
        update_cols = []
        update_vals = []
        
        for i, col in enumerate(nfo_main_cols):
            if col in data:
                update_cols.append(f"{col}=?")
                update_vals.append(nfo_main_vals[i])
                
        if update_cols:
            conn.execute(f"UPDATE nfo_data SET {', '.join(update_cols)} WHERE id = ?", (*update_vals, nfo_id))
        
        # 处理相关映射（演员、类型等）
        from webhook_handler import handle_nfo_mappings
        handle_nfo_mappings(conn.cursor(), nfo_id, data)
        
        conn.commit()
        return jsonify({"success": True, "message": "NFO 已成功保存并更新数据库"})
    except Exception as e:
        conn.rollback()
        current_app.logger.error(f"保存NFO失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"保存失败: {e}"}), 500
    finally:
        conn.close()

@nfo_api.route('/handmade/nfo-details', methods=['GET'])
def get_handmade_nfo_details():
    """获取手作修正模式的NFO详情"""
    nfo_path = request.args.get('path')
    if not nfo_path or not is_safe_path(nfo_path): 
        return jsonify({"error": "无效的NFO路径"}), 400
        
    base_path = os.path.splitext(nfo_path)[0]
    poster_info = image_processor.get_image_details(f"{base_path}-poster.jpg")
    fanart_info = image_processor.get_image_details(f"{base_path}-fanart.jpg")
    thumb_info = image_processor.get_image_details(f"{base_path}-thumb.jpg")
    pictures = {
        "poster_path": f"{base_path}-poster.jpg", "poster_stats": poster_info,
        "fanart_path": f"{base_path}-fanart.jpg", "fanart_stats": fanart_info,
        "thumb_path": f"{base_path}-thumb.jpg", "thumb_stats": thumb_info,
    }
    
    # 使用修改后的NFO解析器获取数据
    nfo_data = parse_nfo_file(nfo_path)
    
    # 确保返回的是可序列化的数据
    if nfo_data and '_nfo_path' in nfo_data:
        # 不需要在JSON中传递内部字段
        nfo_data.pop('_nfo_path', None)
        
    return jsonify({"pictures": pictures, "nfo_data": nfo_data})

@nfo_api.route('/handmade/save-nfo', methods=['POST'])
def save_handmade_nfo():
    """手作修正模式保存NFO文件"""
    nfo_path = request.args.get('path')
    if not nfo_path or not is_safe_path(nfo_path):
        return jsonify({"success": False, "message": "无效的NFO路径"}), 400
    
    try:
        data = request.json
        if not data:
            return jsonify({"success": False, "message": "请求数据为空"}), 400
            
        # 使用'handmade'模式，仅修改NFO文件，不更新数据库
        success, message = save_nfo_file(nfo_path, data, mode='handmade')
        
        if success:
            return jsonify({"success": True, "message": "已保存到NFO文件"})
        else:
            return jsonify({"success": False, "message": message}), 500
    except Exception as e:
        current_app.logger.error(f"保存NFO文件失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"保存NFO文件失败: {e}"}), 500
