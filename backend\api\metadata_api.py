# backend/api/metadata_api.py
"""
元数据获取API模块
负责处理元数据获取、链接验证等功能
"""
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import logging
import xml.etree.ElementTree as ET
import urllib.parse
import re
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

from flask import Blueprint, request, jsonify, current_app
from bs4 import BeautifulSoup

from db_manager import get_db_connection
from config_utils import get_settings
from utils import HTTP_HEADERS

# 创建API蓝图
metadata_api = Blueprint('metadata_api', __name__)

# ==================== 全局配置和工具函数 ====================

def create_optimized_session():
    """创建优化的HTTP会话，用于链接验证"""
    session = requests.Session()
    
    # 配置重试策略
    retry_strategy = Retry(
        total=2,  # 减少重试次数以提高速度
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "OPTIONS"],
        backoff_factor=0.5  # 减少退避时间
    )
    
    adapter = HTTPAdapter(
        max_retries=retry_strategy,
        pool_connections=10,
        pool_maxsize=20,
        pool_block=False
    )
    
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    # 设置默认超时和请求头
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    })

    return session

# 创建全局Session实例，用于链接验证
_http_session = create_optimized_session()

# DMM域名缓存
_dmm_domain_cache = {
    'status': None,  # 'available', 'unavailable', None
    'last_check': None,
    'cache_duration': 300  # 5分钟缓存
}

def check_dmm_domain_availability():
    """检查DMM域名可用性 - 跳过检测，直接返回可用"""
    # 用户确认网站可以访问，跳过域名检测以避免不必要的延迟
    return True

def is_dmm_url(url):
    """判断是否为DMM链接"""
    return url and 'awsimgsrc.dmm.co.jp' in url

class ScrapeError(Exception):
    """用于抓取过程中的错误处理"""
    pass

# ==================== 元数据获取相关功能 ====================

def build_dmm_image_urls(cid: str) -> tuple:
    """
    根据CID构造DMM图片URL

    Args:
        cid: CID字符串

    Returns:
        tuple: (wallpaper_url, cover_url)

    Raises:
        ValueError: 当CID无效或构造URL失败时
    """
    if not cid:
        raise ValueError("CID不能为空")

    try:
        # DMM图片URL需要特殊的数字补位处理
        parts = cid.split('00')
        if len(parts) > 1:
            # 如果CID包含"00"，分割后对最后一部分补位到5位
            code = parts[0] + parts[-1].zfill(5)
        else:
            # 如果CID不包含"00"，需要找到数字部分并补位到5位
            import re
            # 匹配CID中的数字部分（通常在末尾）
            match = re.search(r'(\d+)$', cid)
            if match:
                num_part = match.group(1)
                prefix = cid[:match.start()]
                code = prefix + num_part.zfill(5)
            else:
                # 如果没有找到数字部分，直接使用原CID
                code = cid

        # 确保code不为空
        if not code:
            raise ValueError("生成的code为空")

        wallpaper_url = f"https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{code}/{code}pl.jpg"
        cover_url = f"https://awsimgsrc.dmm.co.jp/pics_dig/digital/video/{code}/{code}ps.jpg"

        return wallpaper_url, cover_url

    except Exception as e:
        raise ValueError(f"构造DMM图片URL失败: CID={cid}, 错误={e}")

def scrape_cid_and_maker_with_matching(bangou: str, target_maker: str) -> tuple:
    """
    从 avbase.net 搜索并解析出 CID 和制作商，支持制作商匹配（仅用于规则管理）

    Args:
        bangou: 番号
        target_maker: 目标制作商

    Returns:
        tuple: (cid, maker) 或 (None, None) 如果未找到
    """
    search_url = f"https://www.avbase.net/works?q={urllib.parse.quote(bangou)}"
    try:
        response = requests.get(search_url, headers=HTTP_HEADERS, timeout=20)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, 'lxml')

        # 查找所有fanza图标
        fanza_imgs = soup.find_all('img', alt='fanza')

        if not fanza_imgs:
            raise ScrapeError(f"在AVBase页面中未找到 'fanza' 图标 (可能无此番号记录或页面结构已更改)")

        # 先收集所有CID，不获取制作商
        candidates = []

        for fanza_img in fanza_imgs:
            try:
                fanza_anchor = fanza_img.find_parent('a')
                if not fanza_anchor or not fanza_anchor.has_attr('href'):
                    continue

                dmm_url_encoded = fanza_anchor['href']
                dmm_url_decoded = urllib.parse.unquote(dmm_url_encoded)

                match = re.search(r'cid=([a-zA-Z0-9_]+)', dmm_url_decoded)
                if not match:
                    continue

                found_cid = match.group(1)

                candidates.append({
                    'cid': found_cid,
                    'maker': None,  # 暂时设为None
                    'url': dmm_url_decoded
                })

            except Exception as e:
                current_app.logger.warning(f"处理fanza条目失败: {e}")
                continue

        if not candidates:
            raise ScrapeError("未能从任何fanza条目中解析出有效的CID")

        # 根据候选数量决定是否需要获取制作商信息
        if len(candidates) == 1:
            # 只有一个候选，从当前页面获取制作商
            try:
                maker_links = soup.find_all('a', href=re.compile(r'/makers/'))
                if maker_links:
                    candidates[0]['maker'] = maker_links[0].get_text(strip=True)
            except Exception as e:
                current_app.logger.warning(f"从搜索页面获取制作商失败: {e}")
        else:
            # 多个候选，需要访问每个CID页面获取准确制作商
            for candidate in candidates:
                try:
                    cid_page_url = f"https://www.avbase.net/works?q={candidate['cid']}"
                    cid_response = requests.get(cid_page_url, headers=HTTP_HEADERS, timeout=10)
                    cid_response.raise_for_status()

                    cid_soup = BeautifulSoup(cid_response.text, 'lxml')
                    maker_links = cid_soup.find_all('a', href=re.compile(r'/makers/'))
                    if maker_links:
                        candidate['maker'] = maker_links[0].get_text(strip=True)

                except Exception as e:
                    current_app.logger.warning(f"通过CID页面获取制作商信息失败: {e}")
                    candidate['maker'] = None

        # 如果没有指定目标制作商，直接返回第一个结果
        if not target_maker:
            selected = candidates[0]
            return selected['cid'], selected['maker']

        # 尝试匹配目标制作商
        for candidate in candidates:
            if candidate['maker']:
                # 尝试多种匹配方式
                candidate_maker = candidate['maker']
                target_lower = target_maker.lower()
                candidate_lower = candidate_maker.lower()

                # 1. 精确匹配
                if candidate_lower == target_lower:
                    return candidate['cid'], candidate['maker']

                # 2. 包含匹配（目标制作商包含在候选制作商中）
                if target_lower in candidate_lower:
                    return candidate['cid'], candidate['maker']

                # 3. 反向包含匹配（候选制作商包含在目标制作商中）
                if candidate_lower in target_lower:
                    return candidate['cid'], candidate['maker']

        # 使用第一个候选结果
        selected = candidates[0]
        return selected['cid'], selected['maker']

    except requests.exceptions.RequestException as e:
        raise ScrapeError(f"网络请求失败: {e}")


def scrape_cid_and_maker(bangou: str) -> tuple:
    """
    从 avbase.net 搜索并解析出 CID（简单查询，不获取制作商）

    注意：为了保持与现有调用代码的兼容性，仍返回tuple格式

    Args:
        bangou: 番号或CID

    Returns:
        tuple: (cid, None) 或 (None, None) 如果未找到
    """
    # AVBase支持直接搜索CID，不需要预处理
    search_url = f"https://www.avbase.net/works?q={urllib.parse.quote(bangou)}"
    try:
        response = requests.get(search_url, headers=HTTP_HEADERS, timeout=20)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, 'lxml')

        # 简单逻辑：只取第一个fanza结果
        fanza_img = soup.find('img', alt='fanza')

        if not fanza_img:
            raise ScrapeError(f"在AVBase页面中未找到 'fanza' 图标 (可能无此番号记录或页面结构已更改)")

        fanza_anchor = fanza_img.find_parent('a')
        if not fanza_anchor or not fanza_anchor.has_attr('href'):
            raise ScrapeError("找到了'fanza'图标，但未能找到其包含链接的父标签")

        dmm_url_encoded = fanza_anchor['href']
        dmm_url_decoded = urllib.parse.unquote(dmm_url_encoded)

        match = re.search(r'cid=([a-zA-Z0-9_]+)', dmm_url_decoded)
        if not match:
            raise ScrapeError(f"在解码后的链接中未能解析出CID: {dmm_url_decoded}")

        found_cid = match.group(1)
        return found_cid, None

    except requests.exceptions.RequestException as e:
        raise ScrapeError(f"网络请求失败: {e}")

def scrape_cid(bangou: str) -> str:
    """
    从 avbase.net 搜索并解析出 CID (向后兼容函数)

    Returns:
        str: CID 或 None 如果未找到
    """
    cid, _ = scrape_cid_and_maker(bangou)
    return cid

def _get_cid_info_common(bangou: str, maker: str = None, operation_name: str = "获取CID信息") -> dict:
    """
    获取CID信息的公共逻辑

    Args:
        bangou: 番号
        maker: 制作商（可选）
        operation_name: 操作名称，用于错误日志

    Returns:
        dict: 包含success和results或error信息的字典
    """
    try:
        # 使用本地CID服务生成CID
        from services.cid_service import cid_service

        result = cid_service.generate_cid(bangou, maker)

        if not result.get("success"):
            return {"success": False, "message": "未找到匹配的规则", "status_code": 404}

        cid = result.get("cid")
        if not cid:
            return {"success": False, "message": "CID生成失败", "status_code": 500}

        # 构造DMM图片URL
        try:
            wallpaper_url, cover_url = build_dmm_image_urls(cid)
        except ValueError as e:
            current_app.logger.error(str(e))
            return {"success": False, "message": str(e), "status_code": 500}

        result_data = {
            "cid": cid,
            "rule_info": {
                "type": result.get("rule_type"),
                "description": result.get("rule_info"),
                "source": "本地规则"
            },
            "wallpaper_url": {"url": wallpaper_url},
            "cover_url": {"url": cover_url}
        }

        return {"success": True, "results": [result_data]}

    except ValueError as e:
        return {"success": False, "message": str(e), "status_code": 404}
    except Exception as e:
        current_app.logger.error(f"{operation_name}失败: {e}", exc_info=True)
        return {"success": False, "message": f"{operation_name}失败: {str(e)}", "status_code": 500}

@metadata_api.route('/get-dmm-info', methods=['GET'])
def get_dmm_info():
    """获取DMM信息 - 使用本地规则生成CID"""
    bangou = request.args.get('bangou')
    maker = request.args.get('maker')  # 可选的制作商参数
    if not bangou:
        return jsonify({"success": False, "message": "需要提供番号"}), 400

    result = _get_cid_info_common(bangou, maker, "获取DMM信息")
    status_code = result.pop("status_code", 200)
    return jsonify(result), status_code

@metadata_api.route('/get-manual-cid-info', methods=['GET'])
def get_manual_cid_info():
    """手动获取CID信息 - 使用本地规则生成CID"""
    bangou = request.args.get('bangou')
    maker = request.args.get('maker')  # 可选的制作商参数
    if not bangou:
        return jsonify({"success": False, "message": "需要提供番号"}), 400

    result = _get_cid_info_common(bangou, maker, "手动获取CID信息")
    status_code = result.pop("status_code", 200)
    return jsonify(result), status_code

@metadata_api.route('/get-movie-maker', methods=['GET'])
def get_movie_maker():
    """获取电影的制作商信息"""
    movie_id = request.args.get('movie_id')
    bangou = request.args.get('bangou')

    if not movie_id and not bangou:
        return jsonify({"success": False, "message": "需要提供movie_id或bangou"}), 400

    try:
        from db_context import db_context

        if movie_id:
            # 通过movie_id查找制作商
            query = """
            SELECT DISTINCT m.name as maker
            FROM nfo_data n
            JOIN nfo_makers nm ON n.id = nm.nfo_id
            JOIN makers m ON nm.maker_id = m.id
            WHERE n.movie_id = ?
            LIMIT 1
            """
            result = db_context.execute_query(query, (movie_id,), fetch_one=True)
        else:
            # 通过bangou查找制作商
            query = """
            SELECT DISTINCT m.name as maker
            FROM movies mov
            JOIN nfo_data n ON mov.id = n.movie_id
            JOIN nfo_makers nm ON n.id = nm.nfo_id
            JOIN makers m ON nm.maker_id = m.id
            WHERE mov.bangou = ?
            LIMIT 1
            """
            result = db_context.execute_query(query, (bangou,), fetch_one=True)

        if result:
            return jsonify({"success": True, "maker": result['maker']})
        else:
            return jsonify({"success": False, "message": "未找到制作商信息"})

    except Exception as e:
        current_app.logger.error(f"获取制作商信息失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"获取制作商信息失败: {str(e)}"}), 500

@metadata_api.route('/get-nfo-maker', methods=['GET'])
def get_nfo_maker():
    """从NFO文件中获取制作商信息"""
    nfo_path = request.args.get('nfo_path')

    if not nfo_path:
        return jsonify({"success": False, "message": "需要提供nfo_path"}), 400

    try:
        from nfo_parser import parse_nfo_file

        # 解析NFO文件
        nfo_data = parse_nfo_file(nfo_path)

        if nfo_data and 'maker' in nfo_data:
            maker = nfo_data['maker']
            if maker and maker.strip():
                return jsonify({"success": True, "maker": maker.strip()})

        return jsonify({"success": False, "message": "NFO文件中未找到制作商信息"})

    except Exception as e:
        current_app.logger.error(f"从NFO获取制作商信息失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"从NFO获取制作商信息失败: {str(e)}"}), 500

# ==================== 链接验证缓存管理 ====================

def get_cached_verification(url):
    """从缓存中获取链接验证结果"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 查询缓存（永久有效，除非强制刷新）
        cursor.execute("""
            SELECT status_code, is_valid, cid
            FROM link_verification_cache
            WHERE url = ?
        """, (url,))

        result = cursor.fetchone()
        conn.close()

        if result:
            return {
                "url": url,
                "status_code": result[0],
                "valid": bool(result[1]),
                "cid": result[2]
            }
        return None
    except Exception as e:
        current_app.logger.error(f"获取缓存失败: {e}")
        return None

def cache_verification_result(url, status_code, is_valid, cid=None):
    """缓存链接验证结果（永久有效）"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            INSERT OR REPLACE INTO link_verification_cache
            (url, cid, status_code, is_valid, verified_at)
            VALUES (?, ?, ?, ?, datetime('now'))
        """, (url, cid, status_code, is_valid))

        conn.commit()
        conn.close()

    except Exception as e:
        current_app.logger.error(f"缓存验证结果失败: {e}")

# ==================== 链接验证相关功能 ====================

@metadata_api.route('/clear-link-cache', methods=['POST'])
def clear_link_cache():
    """清除链接验证缓存"""
    try:
        data = request.get_json()
        if data and 'url' in data:
            # 清除特定URL的缓存
            url = data['url']
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("DELETE FROM link_verification_cache WHERE url = ?", (url,))
            conn.commit()
            conn.close()
            return jsonify({"success": True, "message": f"已清除 {url} 的缓存"})
        else:
            # 清除所有缓存
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("DELETE FROM link_verification_cache")
            conn.commit()
            conn.close()

            # 同时清除DMM域名缓存
            global _dmm_domain_cache
            _dmm_domain_cache['status'] = None
            _dmm_domain_cache['last_check'] = None

            return jsonify({"success": True, "message": "已清除所有链接验证缓存和DMM域名缓存"})
    except Exception as e:
        current_app.logger.error(f"清除缓存失败: {e}")
        return jsonify({"success": False, "message": f"清除缓存失败: {e}"}), 500

@metadata_api.route('/clear-dmm-domain-cache', methods=['POST'])
def clear_dmm_domain_cache():
    """清除DMM域名缓存"""
    try:
        global _dmm_domain_cache
        _dmm_domain_cache['status'] = None
        _dmm_domain_cache['last_check'] = None

        return jsonify({
            "success": True,
            "message": "已清除DMM域名缓存"
        })
    except Exception as e:
        current_app.logger.error(f"清除DMM域名缓存失败: {e}")
        return jsonify({"success": False, "message": f"清除DMM域名缓存失败: {e}"}), 500

@metadata_api.route('/verify-links', methods=['POST'])
def verify_links():
    """
    批量验证链接有效性
    接收链接数组，返回每个链接的验证状态
    支持强制刷新缓存
    """
    try:
        data = request.get_json()
        if not data or 'links' not in data:
            return jsonify({"success": False, "message": "需要提供links数组"}), 400

        links = data['links']
        force_refresh = data.get('force_refresh', False)  # 是否强制刷新缓存
        cid = data.get('cid')  # 可选的CID参数

        if not isinstance(links, list):
            return jsonify({"success": False, "message": "links必须是数组"}), 400

        def verify_single_link(url):
            """验证单个链接的有效性，支持HTTP缓存协商和DMM域名缓存"""

            # 检查URL是否有效
            if not url or url == 'None' or not isinstance(url, str):
                current_app.logger.error(f"无效的URL: {url}")
                return {
                    "url": url,
                    "status_code": 0,
                    "valid": False,
                    "error": "无效的URL"
                }

            # DMM域名优化：如果是DMM链接且域名不可用，直接返回失败
            if is_dmm_url(url) and not check_dmm_domain_availability():
                current_app.logger.debug(f"🚫 DMM域名不可用，跳过验证: {url}")
                return {
                    "url": url,
                    "status_code": 0,
                    "valid": False,
                    "error": "DMM域名不可用"
                }

            # 检查缓存，获取之前的缓存协商头
            cached_result = get_cached_verification(url)

            if not force_refresh and cached_result:
                # 如果不是强制刷新且有缓存，直接返回（永久有效）
                return cached_result

            try:
                try:
                    # 使用4秒超时，适合DMM服务器
                    timeout = 4

                    # 使用更完整的浏览器请求头
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                        'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'Referer': 'https://www.dmm.co.jp/'
                    }

                    # 使用GET请求，stream=False提高稳定性
                    response = _http_session.get(url, timeout=timeout, headers=headers, allow_redirects=True, stream=False)
                    status_code = response.status_code

                except requests.exceptions.Timeout as timeout_e:
                    current_app.logger.error(f"⏰ 请求超时: {timeout_e}")
                    raise timeout_e
                except requests.exceptions.ConnectionError as conn_e:
                    current_app.logger.error(f"🌐 连接错误: {conn_e}")
                    raise conn_e
                except requests.exceptions.RequestException as req_e:
                    current_app.logger.error(f"🚫 请求异常: {req_e}")
                    raise req_e
                except Exception as general_e:
                    current_app.logger.error(f"💥 未知异常: {general_e}")
                    import traceback
                    current_app.logger.error(f"💥 异常堆栈: {traceback.format_exc()}")
                    raise general_e

                # 判断链接是否有效
                is_valid = 200 <= status_code < 400

                result = {
                    "url": url,
                    "status_code": status_code,
                    "valid": is_valid
                }

                # 缓存验证结果（永久有效）
                cache_verification_result(url, status_code, is_valid, cid)

                return result
            except requests.exceptions.Timeout as e:
                current_app.logger.warning(f"⏰ 请求超时 (4秒): {url} - {str(e)}")
                return {
                    "url": url,
                    "status_code": 408,
                    "valid": False,
                    "error": f"请求超时 (4秒): {str(e)}"
                }
            except requests.exceptions.SSLError as e:
                current_app.logger.error(f"🔒 SSL错误: {url} - {str(e)}")
                return {
                    "url": url,
                    "status_code": 0,
                    "valid": False,
                    "error": f"SSL错误: {str(e)}"
                }
            except requests.exceptions.ConnectionError as e:
                current_app.logger.error(f"🌐 连接错误: {url} - {str(e)}")
                return {
                    "url": url,
                    "status_code": 0,
                    "valid": False,
                    "error": f"连接错误: {str(e)}"
                }
            except requests.exceptions.RequestException as e:
                current_app.logger.error(f"🚫 请求异常: {url} - {str(e)}")
                return {
                    "url": url,
                    "status_code": 0,
                    "valid": False,
                    "error": f"请求异常: {str(e)}"
                }
            except Exception as e:
                current_app.logger.error(f"💥 未知异常: {url} - {str(e)}")
                import traceback
                current_app.logger.error(f"💥 异常堆栈: {traceback.format_exc()}")
                return {
                    "url": url,
                    "status_code": 0,
                    "valid": False,
                    "error": f"验证失败: {str(e)}"
                }

        # 并行验证所有链接以提高速度
        import concurrent.futures

        results = []
        valid_links = []

        # 预处理链接，分离DMM和非DMM链接
        dmm_links = []
        other_links = []

        for link in links:
            url = None
            if isinstance(link, str):
                url = link
            elif isinstance(link, dict) and 'url' in link:
                url = link['url']
            else:
                results.append({
                    "url": str(link),
                    "status_code": 0,
                    "valid": False,
                    "error": "无效的链接格式"
                })
                continue

            if is_dmm_url(url):
                dmm_links.append(url)
            else:
                other_links.append(url)

        valid_links = dmm_links + other_links

        # DMM域名批量优化：如果DMM域名不可用，批量标记所有DMM链接为失败
        if dmm_links and not check_dmm_domain_availability():
            current_app.logger.warning(f"🚫 DMM域名不可用，批量跳过{len(dmm_links)}个DMM链接")
            for url in dmm_links:
                results.append({
                    "url": url,
                    "status_code": 0,
                    "valid": False,
                    "error": "DMM域名不可用"
                })
            # 只验证非DMM链接
            valid_links = other_links

        # 并行验证链接
        if valid_links:
            # 在主线程中获取应用实例
            app = current_app._get_current_object()

            # 创建一个包装函数，在Flask应用上下文中执行验证
            def verify_with_context(url):
                try:
                    with app.app_context():
                        return verify_single_link(url)
                except Exception as e:
                    # 使用app.logger而不是current_app.logger
                    with app.app_context():
                        app.logger.error(f"验证链接异常: {url} - {str(e)}")
                    return {
                        "url": url,
                        "status_code": 0,
                        "valid": False,
                        "error": f"验证异常: {str(e)}"
                    }

            # 使用线程池并行验证，最大4个并发
            with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                # 提交所有验证任务
                future_to_url = {executor.submit(verify_with_context, url): url for url in valid_links}

                # 收集结果，保持原始顺序
                url_results = {}
                for future in concurrent.futures.as_completed(future_to_url):
                    url = future_to_url[future]
                    try:
                        result = future.result()
                        url_results[url] = result
                    except Exception as e:
                        # 使用app.logger而不是current_app.logger
                        with app.app_context():
                            app.logger.error(f"并行验证异常: {url} - {str(e)}")
                        url_results[url] = {
                            "url": url,
                            "status_code": 0,
                            "valid": False,
                            "error": f"并行验证异常: {str(e)}"
                        }

                # 按原始顺序添加结果
                for url in valid_links:
                    results.append(url_results[url])

        return jsonify({"success": True, "results": results})

    except Exception as e:
        current_app.logger.error(f"验证链接时发生错误: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"验证链接失败: {e}"}), 500
