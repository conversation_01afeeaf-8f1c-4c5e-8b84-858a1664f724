# backend/api/log_api.py
"""
日志管理API模块
负责处理系统日志的查看、清理等功能
"""
import os
import re
import logging
from flask import Blueprint, request, jsonify, current_app

logger = logging.getLogger(__name__)

# 创建日志管理API蓝图
log_api = Blueprint('log_api', __name__)

@log_api.route('/system-logs', methods=['GET'])
def get_system_logs():
    """获取系统日志文件内容"""
    try:
        log_file_path = os.path.join('logs', 'app.log')
        if not os.path.exists(log_file_path):
            return jsonify({"success": False, "message": "日志文件不存在"}), 404
            
        # 获取查询参数
        max_lines = request.args.get('max_lines', 500, type=int)
        log_level = request.args.get('level', '').upper()  # 可选的日志级别筛选
        
        # 读取日志文件
        with open(log_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 如果设置了日志级别过滤，则只返回匹配的行
        if log_level:
            lines = [line for line in lines if f' {log_level}' in line or f' {log_level}:' in line]
        
        # 返回最后的max_lines行
        logs = lines[-max_lines:] if len(lines) > max_lines else lines
        
        # 解析日志行，提取时间、级别、线程和内容
        parsed_logs = []
        for line in logs:
            try:
                # 标准日志格式通常是: 2025-07-24 12:33:05,219 INFO: 消息内容 [in /app/db_manager.py:176]
                line = line.strip()
                
                # 首先尝试分离时间戳
                timestamp_match = re.match(r'^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3})\s+(.+)$', line)
                if timestamp_match:
                    timestamp = timestamp_match.group(1)
                    content = timestamp_match.group(2)
                else:
                    timestamp = ""
                    content = line
                
                # 然后尝试分离日志级别
                level_match = re.match(r'^(DEBUG|INFO|WARNING|ERROR|CRITICAL):\s+(.+)$', content)
                if level_match:
                    level = level_match.group(1)
                    content = level_match.group(2)
                else:
                    # 可能是其他格式，如"INFO 消息内容"
                    alt_level_match = re.match(r'^(DEBUG|INFO|WARNING|ERROR|CRITICAL)\s+(.+)$', content)
                    if alt_level_match:
                        level = alt_level_match.group(1)
                        content = alt_level_match.group(2)
                    else:
                        level = ""
                
                # 最后提取线程信息，通常在消息末尾 [in /path/file.py:line]
                thread_match = re.search(r'\[in\s+([^\]]+)\]$', content)
                if thread_match:
                    thread = thread_match.group(1)
                    # 从消息中移除线程信息
                    content = content.replace(f'[in {thread}]', '').strip()
                else:
                    thread = ""
                
                parsed_logs.append({
                    'timestamp': timestamp,
                    'level': level,
                    'thread': thread,
                    'message': content
                })
            except Exception as e:
                # 如果解析失败，则添加原始行
                current_app.logger.error(f"解析日志行失败: {str(e)}, 行: {line}")
                parsed_logs.append({
                    'timestamp': '',
                    'level': '',
                    'thread': '',
                    'message': line
                })
                
        return jsonify({
            "success": True, 
            "logs": parsed_logs,
            "total_lines": len(lines)
        })
    except Exception as e:
        current_app.logger.error(f"获取系统日志失败: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"获取系统日志失败: {str(e)}"}), 500

@log_api.route('/system-logs/clear', methods=['POST'])
def clear_system_logs():
    """清空系统日志文件"""
    try:
        log_file_path = os.path.join('logs', 'app.log')
        if os.path.exists(log_file_path):
            # 打开文件并截断为空
            with open(log_file_path, 'w') as f:
                f.write('')
            current_app.logger.info("系统日志已被管理员清除")
            return jsonify({"success": True, "message": "日志已清除"})
        else:
            return jsonify({"success": False, "message": "日志文件不存在"}), 404
    except Exception as e:
        current_app.logger.error(f"清除系统日志失败: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"清除系统日志失败: {str(e)}"}), 500

@log_api.route('/system-logs/info', methods=['GET'])
def get_log_info():
    """获取日志文件信息"""
    try:
        log_file_path = os.path.join('logs', 'app.log')
        if not os.path.exists(log_file_path):
            return jsonify({
                "success": True,
                "exists": False,
                "size": 0,
                "lines": 0
            })
        
        # 获取文件大小
        file_size = os.path.getsize(log_file_path)
        
        # 计算行数
        with open(log_file_path, 'r', encoding='utf-8') as f:
            line_count = sum(1 for _ in f)
        
        return jsonify({
            "success": True,
            "exists": True,
            "size": file_size,
            "lines": line_count,
            "path": log_file_path
        })
    except Exception as e:
        current_app.logger.error(f"获取日志文件信息失败: {str(e)}", exc_info=True)
        return jsonify({"success": False, "message": f"获取日志文件信息失败: {str(e)}"}), 500
