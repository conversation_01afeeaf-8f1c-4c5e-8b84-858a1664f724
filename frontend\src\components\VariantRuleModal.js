import React, { useState, useEffect } from 'react';
import {
  XMarkIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline';
import { cidApi } from '../api/cid';

const VariantRuleModal = ({ mainRule, initialData, onClose, onRefresh }) => {
  const [variantRules, setVariantRules] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showVariantForm, setShowVariantForm] = useState(false);
  const [editingVariant, setEditingVariant] = useState(null);
  const [variantForm, setVariantForm] = useState({
    maker: '',
    maker_enabled: false,
    serial_number_range: '',
    cid_prefix: '',
    series_name_in_cid: '',
    number_padding: 0,
    suffix: '',
    description: ''
  });

  // 加载变体规则
  const loadVariantRules = async () => {
    if (!mainRule) return;

    setLoading(true);
    try {
      const response = await cidApi.getVariantRules(mainRule.id);
      if (response.success) {
        setVariantRules(response.variants);
      } else {
        console.error('加载变体规则失败:', response.message);
      }
    } catch (error) {
      console.error('加载变体规则失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 显示创建变体规则表单
  const showCreateVariant = () => {
    setEditingVariant(null);
    setVariantForm({
      maker: '',
      maker_enabled: false,
      serial_number_range: '',
      cid_prefix: mainRule?.cid_prefix || '',
      series_name_in_cid: mainRule?.series_name?.toLowerCase() || '',
      number_padding: mainRule?.number_padding || 0,
      suffix: mainRule?.suffix || '',
      description: ''
    });
    setShowVariantForm(true);
  };

  // 显示编辑变体规则表单
  const showEditVariant = (variant) => {
    setEditingVariant(variant);
    setVariantForm({
      ...variant,
      maker_enabled: Number(variant.maker_enabled) === 1 // 确保正确转换数据库的数字值
    });
    setShowVariantForm(true);
  };

  // 保存变体规则
  const saveVariantRule = async () => {
    try {
      const data = { ...variantForm };
      delete data.id;

      // 转换maker_enabled为数字
      data.maker_enabled = data.maker_enabled ? 1 : 0;

      // 如果制作商开关关闭，清空制作商字段
      if (!data.maker_enabled) {
        data.maker = '';
      }

      let response;
      if (editingVariant) {
        response = await cidApi.updateVariantRule(editingVariant.id, data);
      } else {
        response = await cidApi.createVariantRule(mainRule.id, data);
      }

      if (response.success) {
        setShowVariantForm(false);
        loadVariantRules();
        onRefresh();
        alert(response.message);
      } else {
        alert(response.message);
      }
    } catch (error) {
      console.error('保存变体规则失败:', error);
      alert('保存变体规则失败');
    }
  };

  // 删除变体规则
  const deleteVariantRule = async (variant) => {
    if (!window.confirm(`确定要删除变体规则 "${variant.description}" 吗？`)) {
      return;
    }

    try {
      const response = await cidApi.deleteVariantRule(variant.id);
      if (response.success) {
        loadVariantRules();
        onRefresh();
        alert(response.message);
      } else {
        alert(response.message);
      }
    } catch (error) {
      console.error('删除变体规则失败:', error);
      alert('删除变体规则失败');
    }
  };

  // 调整优先级
  const adjustPriority = async (variant, direction) => {
    const currentIndex = variantRules.findIndex(v => v.id === variant.id);
    if (
      (direction === 'up' && currentIndex === 0) ||
      (direction === 'down' && currentIndex === variantRules.length - 1)
    ) {
      return;
    }

    const newRules = [...variantRules];
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    
    // 交换位置
    [newRules[currentIndex], newRules[targetIndex]] = [newRules[targetIndex], newRules[currentIndex]];
    
    // 更新优先级（从0开始）
    const variantOrders = newRules.map((rule, index) => ({
      id: rule.id,
      priority: index
    }));

    try {
      const response = await cidApi.reorderVariantRules({
        variant_orders: variantOrders
      });

      if (response.success) {
        loadVariantRules();
      } else {
        alert(response.message);
      }
    } catch (error) {
      console.error('调整优先级失败:', error);
      alert('调整优先级失败');
    }
  };



  useEffect(() => {
    loadVariantRules();

    // 如果有初始数据，自动打开创建表单
    if (initialData) {
      setVariantForm({
        ...initialData,
        maker_enabled: Number(initialData.maker_enabled) === 1 // 确保正确转换数据库的数字值
      });
      setEditingVariant(null);
      setShowVariantForm(true);
    }
  }, [mainRule, initialData]);

  if (!mainRule) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
        {/* 头部 */}
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium text-gray-900">变体规则管理</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* 主规则信息 */}
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <h4 className="text-md font-medium text-gray-900 mb-3">主规则信息</h4>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">番号前缀:</span>
              <span className="ml-2 text-gray-900">{mainRule.series_name}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">CID前缀:</span>
              <span className="ml-2 text-gray-900">{mainRule.cid_prefix || '-'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">CID系列名:</span>
              <span className="ml-2 text-gray-900">{mainRule.series_name_in_cid || '-'}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">补位规则:</span>
              <span className="ml-2 text-gray-900">
                {mainRule.number_padding === 0 ? '保持原编号' : `补位到${mainRule.number_padding}位`}
              </span>
            </div>
            <div>
              <span className="font-medium text-gray-700">后缀:</span>
              <span className="ml-2 text-gray-900">{mainRule.suffix || '-'}</span>
            </div>
          </div>
        </div>

        {/* 变体规则列表 */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <h4 className="text-md font-medium text-gray-900">变体规则列表</h4>
            <button
              onClick={showCreateVariant}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <PlusIcon className="w-4 h-4" />
              新增变体规则
            </button>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full border border-gray-200 rounded-lg">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">优先级</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">制作商</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">范围条件</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">CID前缀</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">CID系列名</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">补位规则</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">后缀</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">描述</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">操作</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan="9" className="px-4 py-4 text-center text-gray-500">
                      加载中...
                    </td>
                  </tr>
                ) : variantRules.length === 0 ? (
                  <tr>
                    <td colSpan="9" className="px-4 py-4 text-center text-gray-500">
                      暂无变体规则
                    </td>
                  </tr>
                ) : (
                  variantRules.map((variant, index) => (
                    <tr key={variant.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center gap-2">
                          <span>{variant.priority}</span>
                          <div className="flex flex-col">
                            <button
                              onClick={() => adjustPriority(variant, 'up')}
                              disabled={index === 0}
                              className="text-gray-400 hover:text-gray-600 disabled:opacity-30"
                            >
                              <ArrowUpIcon className="w-3 h-3" />
                            </button>
                            <button
                              onClick={() => adjustPriority(variant, 'down')}
                              disabled={index === variantRules.length - 1}
                              className="text-gray-400 hover:text-gray-600 disabled:opacity-30"
                            >
                              <ArrowDownIcon className="w-3 h-3" />
                            </button>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                        {Number(variant.maker_enabled) === 1 ? (
                          <span className="inline-flex items-center">
                            <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                            {variant.maker || '未设置'}
                          </span>
                        ) : (
                          <span className="inline-flex items-center text-gray-400">
                            <span className="w-2 h-2 bg-gray-300 rounded-full mr-2"></span>
                            已禁用
                          </span>
                        )}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                        {variant.serial_number_range || '-'}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                        {variant.cid_prefix || '-'}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                        {variant.series_name_in_cid || '-'}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                        {variant.number_padding === 0 ? '保持原编号' : `补位到${variant.number_padding}位`}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                        {variant.suffix || '-'}
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-500 max-w-xs truncate">
                        {variant.description || '-'}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => showEditVariant(variant)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            <PencilIcon className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => deleteVariantRule(variant)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* 变体规则表单模态框 */}
        {showVariantForm && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-60">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  {editingVariant ? '编辑变体规则' : '新增变体规则'}
                </h3>

                <div className="space-y-4">
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <label className="block text-sm font-medium text-gray-700">制作商</label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={variantForm.maker_enabled}
                          onChange={(e) => setVariantForm(prev => ({
                            ...prev,
                            maker_enabled: e.target.checked,
                            maker: e.target.checked ? prev.maker : ''
                          }))}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-600">启用制作商匹配</span>
                      </label>
                    </div>
                    <input
                      type="text"
                      value={variantForm.maker}
                      onChange={(e) => setVariantForm(prev => ({ ...prev, maker: e.target.value }))}
                      disabled={!variantForm.maker_enabled}
                      className={`mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500 ${
                        !variantForm.maker_enabled ? 'bg-gray-100 text-gray-400' : ''
                      }`}
                      placeholder={variantForm.maker_enabled ? "如SWITCH、SOD等" : "制作商匹配已禁用"}
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      {variantForm.maker_enabled
                        ? "启用时：必须匹配指定制作商才生效"
                        : "禁用时：忽略制作商，只根据范围条件和优先级匹配"
                      }
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">范围条件</label>
                    <input
                      type="text"
                      value={variantForm.serial_number_range}
                      onChange={(e) => setVariantForm(prev => ({ ...prev, serial_number_range: e.target.value }))}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="如&gt;=100、&lt;=500、1-100、100,200等"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      支持格式：大于等于（&gt;=100）、小于等于（&lt;=500）、范围（1-500）、列表（100,200,300）、混合（529,641-642,676）
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">CID前缀</label>
                    <input
                      type="text"
                      value={variantForm.cid_prefix}
                      onChange={(e) => setVariantForm(prev => ({ ...prev, cid_prefix: e.target.value }))}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="如h_635"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">CID系列名</label>
                    <input
                      type="text"
                      value={variantForm.series_name_in_cid}
                      onChange={(e) => setVariantForm(prev => ({ ...prev, series_name_in_cid: e.target.value }))}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="如sw"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">补位规则</label>
                    <select
                      value={variantForm.number_padding}
                      onChange={(e) => setVariantForm(prev => ({ ...prev, number_padding: parseInt(e.target.value) }))}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value={0}>保持原编号</option>
                      <option value={5}>补位到5位</option>
                      <option value={6}>补位到6位</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">后缀</label>
                    <input
                      type="text"
                      value={variantForm.suffix}
                      onChange={(e) => setVariantForm(prev => ({ ...prev, suffix: e.target.value }))}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="如re"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700">描述</label>
                    <textarea
                      value={variantForm.description}
                      onChange={(e) => setVariantForm(prev => ({ ...prev, description: e.target.value }))}
                      rows={3}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 mt-6">
                  <button
                    onClick={() => setShowVariantForm(false)}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                  >
                    取消
                  </button>
                  <button
                    onClick={saveVariantRule}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    保存
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VariantRuleModal;
