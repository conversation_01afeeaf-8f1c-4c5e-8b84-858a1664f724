import { useState, useEffect } from 'react';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  TrashIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import VariantRuleModal from '../components/VariantRuleModal';
import { cidApi } from '../api/cid';

const CidManagementPage = () => {
  const [mainRules, setMainRules] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [pagination, setPagination] = useState({
    page: 1,
    per_page: 15,
    total: 0,
    pages: 0
  });
  
  // 主规则编辑状态
  const [showMainRuleModal, setShowMainRuleModal] = useState(false);
  const [editingMainRule, setEditingMainRule] = useState(null);
  const [mainRuleForm, setMainRuleForm] = useState({
    series_name: '',
    cid_prefix: '',
    series_name_in_cid: '',
    number_padding: 0,
    suffix: '',
    description: ''
  });
  
  // 变体规则状态
  const [showVariantModal, setShowVariantModal] = useState(false);
  const [selectedMainRule, setSelectedMainRule] = useState(null);
  const [initialVariantData, setInitialVariantData] = useState(null);

  // CID生成测试状态
  const [testForm, setTestForm] = useState({
    bangou: '',
    maker: ''
  });
  const [testResult, setTestResult] = useState(null);
  const [testing, setTesting] = useState(false);

  // AVBase查询状态
  const [avbaseResult, setAvbaseResult] = useState(null);
  const [lookingUp, setLookingUp] = useState(false);

  // 批量导入状态
  const [showBatchImportModal, setShowBatchImportModal] = useState(false);
  const [batchImportText, setBatchImportText] = useState('');
  const [importing, setImporting] = useState(false);

  // 加载主规则列表
  const loadMainRules = async () => {
    setLoading(true);
    try {
      const params = {
        page: pagination.page,
        per_page: pagination.per_page,
        search: searchTerm
      };

      const response = await cidApi.getMainRules(params);

      if (response.success) {
        setMainRules(response.data);
        setPagination(response.pagination);
      } else {
        console.error('加载主规则失败:', response.message);
        alert(`加载主规则失败: ${response.message}`);
      }
    } catch (error) {
      console.error('加载主规则失败:', error);
      alert('加载主规则失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  // 搜索处理
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    loadMainRules();
  };

  // 重置搜索
  const resetSearch = () => {
    setSearchTerm('');
    setPagination(prev => ({ ...prev, page: 1 }));
    setTimeout(loadMainRules, 0);
  };

  // 显示创建主规则对话框
  const showCreateMainRule = () => {
    setEditingMainRule(null);
    setMainRuleForm({
      series_name: '',
      cid_prefix: '',
      series_name_in_cid: '',
      number_padding: 0,
      suffix: '',
      description: ''
    });
    setShowMainRuleModal(true);
  };

  // 显示编辑主规则对话框
  const showEditMainRule = (rule) => {
    setEditingMainRule(rule);
    setMainRuleForm({ ...rule });
    setShowMainRuleModal(true);
  };

  // 保存主规则
  const saveMainRule = async () => {
    try {
      const data = { ...mainRuleForm };
      delete data.id;

      let response;
      if (editingMainRule) {
        response = await cidApi.updateMainRule(editingMainRule.id, data);
      } else {
        response = await cidApi.createMainRule(data);
      }

      if (response.success) {
        setShowMainRuleModal(false);
        loadMainRules();
        alert(response.message);
      } else {
        alert(response.message);
      }
    } catch (error) {
      console.error('保存主规则失败:', error);
      alert('保存主规则失败');
    }
  };

  // 删除主规则
  const deleteMainRule = async (rule) => {
    if (!window.confirm(`确定要删除主规则 "${rule.series_name}" 吗？这将同时删除所有相关的变体规则。`)) {
      return;
    }

    try {
      const response = await cidApi.deleteMainRule(rule.id);
      if (response.success) {
        loadMainRules();
        alert(response.message);
      } else {
        alert(response.message);
      }
    } catch (error) {
      console.error('删除主规则失败:', error);
      alert('删除主规则失败');
    }
  };

  // 显示变体规则管理
  const showVariantRules = (rule) => {
    setSelectedMainRule(rule);
    setShowVariantModal(true);
  };

  // CID生成测试
  const testCidGeneration = async () => {
    if (!testForm.bangou.trim()) {
      alert('请输入番号');
      return;
    }

    setTesting(true);
    try {
      const response = await cidApi.generateCid({
        bangou: testForm.bangou,
        maker: testForm.maker
      });

      if (response.success) {
        setTestResult(response);
      } else {
        alert(response.message);
        setTestResult(null);
      }
    } catch (error) {
      console.error('CID生成测试失败:', error);
      alert('CID生成测试失败');
      setTestResult(null);
    } finally {
      setTesting(false);
    }
  };

  // AVBase查询
  const avbaseLookup = async () => {
    if (!testForm.bangou.trim()) {
      alert('请输入番号');
      return;
    }

    setLookingUp(true);
    try {
      const response = await cidApi.avbaseLookup({
        bangou: testForm.bangou,
        maker: testForm.maker
      });

      if (response.success) {
        setAvbaseResult(response);
        setTestResult(null); // 清空生成结果
      } else {
        alert(response.message);
      }
    } catch (error) {
      console.error('AVBase查询失败:', error);
      alert('AVBase查询失败');
    } finally {
      setLookingUp(false);
    }
  };

  // 从AVBase结果创建主规则
  const createMainRuleFromAvbase = () => {
    if (avbaseResult) {
      const ruleData = avbaseResult.derived_rule;
      setMainRuleForm({
        series_name: ruleData.series_name || '',
        cid_prefix: ruleData.prefix || '',
        series_name_in_cid: ruleData.series_name_in_cid || '',
        number_padding: ruleData.padding || 0,
        suffix: ruleData.suffix || '',
        description: '从AVBase查询推导的规则'
      });
      setEditingMainRule(null);
      setShowMainRuleModal(true);
    }
  };

  // 从AVBase结果创建变体规则
  const createVariantRuleFromAvbase = () => {
    if (avbaseResult && avbaseResult.suggestion === '变体规则') {
      let mainRule = null;

      // 如果有main_rule_id，直接查找
      if (avbaseResult.main_rule_id) {
        mainRule = mainRules.find(rule => rule.id === avbaseResult.main_rule_id);
      }
      // 否则通过existing_rule查找
      else if (avbaseResult.existing_rule) {
        mainRule = avbaseResult.existing_rule;
      }

      if (mainRule) {
        const ruleData = avbaseResult.derived_rule;
        // 设置初始变体规则数据
        const variantData = {
          maker: '',
          maker_enabled: false,
          serial_number_range: avbaseResult.bangou.match(/\d+$/)?.[0] || '', // 提取数字部分
          cid_prefix: ruleData.prefix || mainRule.cid_prefix || '',
          series_name_in_cid: mainRule.series_name?.toLowerCase() || ruleData.series_name_in_cid || '',
          number_padding: ruleData.padding || mainRule.number_padding || 0,
          suffix: ruleData.suffix || mainRule.suffix || '',
          description: `从AVBase查询推导的变体规则 (${avbaseResult.bangou})`
        };

        setInitialVariantData(variantData);
        setSelectedMainRule(mainRule);
        setShowVariantModal(true);
      } else {
        alert('找不到对应的主规则');
      }
    }
  };

  // 清空测试结果
  const clearTestResult = () => {
    setTestForm({ bangou: '', maker: '' });
    setTestResult(null);
    setAvbaseResult(null);
  };

  // 批量导入主规则
  const batchImportRules = async () => {
    if (!batchImportText.trim()) {
      alert('请输入规则数据');
      return;
    }

    setImporting(true);
    try {
      const response = await cidApi.batchImportRules({
        rules_text: batchImportText
      });

      if (response.success) {
        alert(response.message);
        if (response.errors && response.errors.length > 0) {
          console.warn('导入过程中的错误:', response.errors);
        }
        setShowBatchImportModal(false);
        setBatchImportText('');
        loadMainRules(); // 刷新列表
      } else {
        alert(response.message);
      }
    } catch (error) {
      console.error('批量导入失败:', error);
      alert('批量导入失败');
    } finally {
      setImporting(false);
    }
  };

  // 分页处理
  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  // 处理URL参数
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const action = urlParams.get('action');
    const data = urlParams.get('data');

    if (action === 'create' && data) {
      try {
        const ruleData = JSON.parse(decodeURIComponent(data));
        setMainRuleForm({
          series_name: ruleData.series_name || '',
          cid_prefix: ruleData.prefix || '',
          series_name_in_cid: ruleData.series_name_in_cid || '',
          number_padding: ruleData.padding || 0,
          suffix: ruleData.suffix || '',
          description: '从AVBase查询推导的规则'
        });
        setEditingMainRule(null);
        setShowMainRuleModal(true);

        // 清除URL参数
        window.history.replaceState({}, document.title, window.location.pathname);
      } catch (error) {
        console.error('解析URL参数失败:', error);
      }
    } else if (action === 'create-variant' && data) {
      try {
        const ruleData = JSON.parse(decodeURIComponent(data));
        const mainRuleId = urlParams.get('mainRuleId');

        // 这里可以处理创建变体规则的逻辑
        console.log('创建变体规则:', { mainRuleId, ruleData });

        // 清除URL参数
        window.history.replaceState({}, document.title, window.location.pathname);
      } catch (error) {
        console.error('解析URL参数失败:', error);
      }
    }
  }, []);



  useEffect(() => {
    loadMainRules();
  }, [pagination.page, pagination.per_page]);

  return (
    <div className="p-6 bg-[var(--color-primary-bg)] min-h-screen">
      {/* 页面头部 */}
      <div className="flex justify-between items-start mb-6">
        <h1 className="text-2xl font-bold text-[var(--color-primary-text)]">规则管理</h1>
        <div className="text-right">
          <div className="flex gap-3 mb-2">
          <button
            onClick={showCreateMainRule}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <PlusIcon className="w-5 h-5" />
            新增主规则
          </button>
          <button
            onClick={() => setShowBatchImportModal(true)}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <PlusIcon className="w-5 h-5" />
            批量导入
          </button>
          </div>

        </div>
      </div>

      {/* 搜索区域 */}
      <div className="bg-[var(--color-secondary-bg)] p-4 rounded-lg mb-6">
        <div className="flex items-center gap-4">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="输入番号前缀搜索"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <button
            onClick={handleSearch}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            搜索
          </button>
          <button
            onClick={resetSearch}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            重置
          </button>
        </div>
      </div>

      {/* CID生成测试区域 */}
      <div className="bg-[var(--color-secondary-bg)] rounded-lg p-6 mb-6">
        <h2 className="text-lg font-semibold text-[var(--color-primary-text)] mb-4">CID生成测试</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 测试表单 */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-[var(--color-primary-text)] mb-2">
                番号 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={testForm.bangou}
                onChange={(e) => setTestForm(prev => ({ ...prev, bangou: e.target.value }))}
                onKeyDown={(e) => e.key === 'Enter' && testCidGeneration()}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入番号，如SW-123"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-[var(--color-primary-text)] mb-2">
                制作商
              </label>
              <input
                type="text"
                value={testForm.maker}
                onChange={(e) => setTestForm(prev => ({ ...prev, maker: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="制作商信息（可选）"
              />
            </div>

            <div className="flex gap-3">
              <button
                onClick={testCidGeneration}
                disabled={testing}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
              >
                {testing ? '测试中...' : '测试生成'}
              </button>
              <button
                onClick={avbaseLookup}
                disabled={lookingUp}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                <MagnifyingGlassIcon className="w-5 h-5" />
                {lookingUp ? '查询中...' : 'AVBase查询'}
              </button>
              <button
                onClick={clearTestResult}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                清空
              </button>
            </div>
          </div>

          {/* 测试结果 */}
          <div className="max-h-80 overflow-y-auto">
            {testResult ? (
              <div className="space-y-3">
                <h3 className="font-medium text-[var(--color-primary-text)]">生成结果</h3>
                <div className="bg-gray-50 p-4 rounded-lg space-y-2 text-sm">
                  <div><strong>番号:</strong> {testResult.bangou}</div>
                  <div><strong>CID:</strong> <span className="font-mono text-blue-600">{testResult.cid}</span></div>
                  <div><strong>使用规则:</strong> {testResult.rule_info}</div>
                  <div><strong>规则类型:</strong>
                    <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      testResult.rule_type === 'main'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {testResult.rule_type === 'main' ? '主规则' : '变体规则'}
                    </span>
                  </div>
                  <div><strong>制作商:</strong> {testResult.maker_used}</div>
                </div>
              </div>
            ) : avbaseResult ? (
              <div className="space-y-3">
                <h3 className="font-medium text-[var(--color-primary-text)]">AVBase查询结果</h3>
                <div className="bg-gray-50 p-4 rounded-lg space-y-2 text-sm max-h-60 overflow-y-auto">
                  <div><strong>番号:</strong> {avbaseResult.bangou}</div>
                  <div><strong>查询到的CID:</strong> <span className="font-mono text-blue-600">{avbaseResult.cid}</span></div>
                  {avbaseResult.maker && (
                    <div><strong>制作商:</strong> {avbaseResult.maker}</div>
                  )}
                  <div><strong>推导规则:</strong></div>
                  <div className="ml-4 space-y-1">
                    <div>• 番号前缀: {avbaseResult.derived_rule.series_name}</div>
                    <div>• CID规则: [{avbaseResult.derived_rule.prefix || '无'}][{avbaseResult.derived_rule.series_name_in_cid}][{avbaseResult.derived_rule.padding === 0 ? '保持原编号' : `补位到${avbaseResult.derived_rule.padding}位`}][{avbaseResult.derived_rule.suffix || '无'}]</div>
                  </div>
                  <div><strong>建议操作:</strong>
                    <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      avbaseResult.suggestion === '新规则'
                        ? 'bg-yellow-100 text-yellow-800'
                        : avbaseResult.suggestion === '变体规则'
                        ? 'bg-blue-100 text-blue-800'
                        : avbaseResult.suggestion === 'CID重复'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {avbaseResult.suggestion}
                    </span>
                  </div>
                </div>

                {/* AVBase操作按钮 */}
                <div className="flex gap-2 mt-3">
                  {avbaseResult.suggestion === '新规则' ? (
                    <button
                      onClick={createMainRuleFromAvbase}
                      className="px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700 transition-colors"
                    >
                      创建主规则
                    </button>
                  ) : avbaseResult.suggestion === '变体规则' ? (
                    <button
                      onClick={createVariantRuleFromAvbase}
                      className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                    >
                      创建变体规则
                    </button>
                  ) : avbaseResult.suggestion === 'CID重复' ? (
                    <span className="px-3 py-1 bg-red-100 text-red-800 text-sm rounded">
                      CID规则重复，无法添加
                    </span>
                  ) : (
                    <span className="px-3 py-1 bg-green-100 text-green-800 text-sm rounded">
                      规则已完全匹配，无需操作
                    </span>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-gray-500 text-center py-8">
                <div className="mb-2">输入番号并点击"测试生成"或"AVBase查询"查看结果</div>
                <div className="text-sm text-gray-400">
                  匹配优先级：制作商精确匹配的变体规则 &gt; 其他变体规则（数字越小越优先） &gt; 主规则
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 主规则列表 */}
      <div className="bg-[var(--color-secondary-bg)] rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  番号前缀
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  CID前缀
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  CID系列名
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  补位规则
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  后缀
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  描述
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  变体规则
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan="8" className="px-6 py-4 text-center text-gray-500">
                    加载中...
                  </td>
                </tr>
              ) : mainRules.length === 0 ? (
                <tr>
                  <td colSpan="8" className="px-6 py-4 text-center text-gray-500">
                    暂无数据
                  </td>
                </tr>
              ) : (
                mainRules.map((rule) => (
                  <tr key={rule.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {rule.series_name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {rule.cid_prefix || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {rule.series_name_in_cid || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {rule.number_padding === 0 ? '保持原编号' : `补位到${rule.number_padding}位`}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {rule.suffix || '-'}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate" title={rule.description}>
                      {rule.description || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {rule.variant_count > 0 ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {rule.variant_count}个
                        </span>
                      ) : (
                        '-'
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => showEditMainRule(rule)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <PencilIcon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => showVariantRules(rule)}
                          className="text-green-600 hover:text-green-900"
                        >
                          <Cog6ToothIcon className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => deleteMainRule(rule)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* 分页 */}
        {pagination.pages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page <= 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                上一页
              </button>
              <button
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page >= pagination.pages}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                下一页
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  显示第 <span className="font-medium">{(pagination.page - 1) * pagination.per_page + 1}</span> 到{' '}
                  <span className="font-medium">
                    {Math.min(pagination.page * pagination.per_page, pagination.total)}
                  </span>{' '}
                  条，共 <span className="font-medium">{pagination.total}</span> 条记录
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                  {/* 上一页按钮 */}
                  <button
                    onClick={() => handlePageChange(pagination.page - 1)}
                    disabled={pagination.page <= 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    上一页
                  </button>

                  {/* 页码按钮 */}
                  {(() => {
                    const currentPage = pagination.page;
                    const totalPages = pagination.pages;
                    const maxVisiblePages = 7; // 最多显示7个页码

                    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
                    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

                    // 调整起始页，确保显示足够的页码
                    if (endPage - startPage + 1 < maxVisiblePages) {
                      startPage = Math.max(1, endPage - maxVisiblePages + 1);
                    }

                    const pages = [];

                    // 第一页
                    if (startPage > 1) {
                      pages.push(
                        <button
                          key={1}
                          onClick={() => handlePageChange(1)}
                          className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                        >
                          1
                        </button>
                      );

                      if (startPage > 2) {
                        pages.push(
                          <span key="start-ellipsis" className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                            ...
                          </span>
                        );
                      }
                    }

                    // 中间页码
                    for (let page = startPage; page <= endPage; page++) {
                      pages.push(
                        <button
                          key={page}
                          onClick={() => handlePageChange(page)}
                          className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                            page === currentPage
                              ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                              : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      );
                    }

                    // 最后一页
                    if (endPage < totalPages) {
                      if (endPage < totalPages - 1) {
                        pages.push(
                          <span key="end-ellipsis" className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                            ...
                          </span>
                        );
                      }

                      pages.push(
                        <button
                          key={totalPages}
                          onClick={() => handlePageChange(totalPages)}
                          className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                        >
                          {totalPages}
                        </button>
                      );
                    }

                    return pages;
                  })()}

                  {/* 下一页按钮 */}
                  <button
                    onClick={() => handlePageChange(pagination.page + 1)}
                    disabled={pagination.page >= pagination.pages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  >
                    下一页
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 主规则编辑模态框 */}
      {showMainRuleModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingMainRule ? '编辑主规则' : '新增主规则'}
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">番号前缀</label>
                  <input
                    type="text"
                    value={mainRuleForm.series_name}
                    onChange={(e) => setMainRuleForm(prev => ({ ...prev, series_name: e.target.value }))}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="如SW"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">CID前缀</label>
                  <input
                    type="text"
                    value={mainRuleForm.cid_prefix}
                    onChange={(e) => setMainRuleForm(prev => ({ ...prev, cid_prefix: e.target.value }))}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="如h_635"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">CID系列名</label>
                  <input
                    type="text"
                    value={mainRuleForm.series_name_in_cid}
                    onChange={(e) => setMainRuleForm(prev => ({ ...prev, series_name_in_cid: e.target.value }))}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="如sw"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">补位规则</label>
                  <select
                    value={mainRuleForm.number_padding}
                    onChange={(e) => setMainRuleForm(prev => ({ ...prev, number_padding: parseInt(e.target.value) }))}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value={0}>保持原编号</option>
                    <option value={5}>补位到5位</option>
                    <option value={6}>补位到6位</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">后缀</label>
                  <input
                    type="text"
                    value={mainRuleForm.suffix}
                    onChange={(e) => setMainRuleForm(prev => ({ ...prev, suffix: e.target.value }))}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="如re"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">描述</label>
                  <textarea
                    value={mainRuleForm.description}
                    onChange={(e) => setMainRuleForm(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                

              </div>
              
              <div className="flex justify-end gap-3 mt-6">
                <button
                  onClick={() => setShowMainRuleModal(false)}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={saveMainRule}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  保存
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 变体规则管理模态框 */}
      {showVariantModal && (
        <VariantRuleModal
          mainRule={selectedMainRule}
          initialData={initialVariantData}
          onClose={() => {
            setShowVariantModal(false);
            setInitialVariantData(null);
          }}
          onRefresh={loadMainRules}
        />
      )}

      {/* 批量导入模态框 */}
      {showBatchImportModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">批量导入主规则</h3>

              <div className="mb-4">
                <p className="text-sm text-gray-600 mb-2">
                  请按以下格式输入规则数据，每行一个规则：
                </p>
                <p className="text-xs text-gray-500 mb-2">
                  格式：番号前缀,CID前缀,CID系列名,补位规则,后缀,描述
                </p>
                <p className="text-xs text-gray-500 mb-4">
                  示例：SW,1,sw,5,-,SWITCH系列
                </p>

                <textarea
                  value={batchImportText}
                  onChange={(e) => setBatchImportText(e.target.value)}
                  className="w-full h-64 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="SW,1,sw,5,-,SWITCH系列&#10;ABP,h_,abp,3,re,ABP系列&#10;SSIS,1,ssis,3,-,SSIS系列"
                />
              </div>

              <div className="flex justify-end gap-3">
                <button
                  onClick={() => {
                    setShowBatchImportModal(false);
                    setBatchImportText('');
                  }}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={batchImportRules}
                  disabled={importing}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:opacity-50"
                >
                  {importing ? '导入中...' : '开始导入'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CidManagementPage;
