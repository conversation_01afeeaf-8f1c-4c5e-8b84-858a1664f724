# backend/api/media_api.py
"""
媒体文件服务API模块
负责处理媒体文件和水印文件的访问服务
"""
import os
import logging
from flask import Blueprint, send_from_directory, current_app
from config_utils import get_settings
from utils import is_safe_path as utils_is_safe_path

logger = logging.getLogger(__name__)

# 创建媒体API蓝图
media_api = Blueprint('media_api', __name__)

def get_media_root():
    """获取媒体根路径"""
    return get_settings().get('media_root', '/weiam')

def is_safe_path(path):
    """
    检查请求路径是否在允许的媒体根目录内的包装函数
    
    Args:
        path: 要检查的路径
    
    Returns:
        bool: 如果路径安全则返回True，否则返回False
    """
    return utils_is_safe_path(path, get_media_root())

@media_api.route('/media/<path:filename>')
def serve_media_file(filename):
    """
    媒体文件服务路由
    处理媒体文件的访问请求，包括封面缓存和普通媒体文件
    """
    # 添加调试日志
    current_app.logger.debug(f"请求访问文件: {filename}")
    
    try:
        # 特殊处理：封面缓存路径
        if filename.startswith('cover_cache/'):
            directory = 'cover_cache'
            name = filename.replace('cover_cache/', '')
            
            # 安全检查：防止路径遍历攻击
            if '..' in name or name.startswith('/'):
                current_app.logger.warning(f"检测到可能的路径遍历尝试: {name}")
                return "Forbidden", 403
            
            current_app.logger.debug(f"访问缓存文件: 目录={directory}, 文件名={name}")
            
            # 确保目录存在
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                current_app.logger.debug(f"创建缓存目录: {directory}")
            
            # 检查文件是否存在
            full_path = os.path.join(directory, name)
            if not os.path.exists(full_path):
                current_app.logger.warning(f"缓存文件不存在: {full_path}，请使用刷新缓存功能重新获取")
                
                # 返回404而不是错误消息，这样前端会显示占位图像
                return "File not found", 404
            
            # 文件存在，发送文件
            return send_from_directory(directory, name, as_attachment=False)
        
        # 构建完整的文件路径
        media_root = get_media_root()

        # 智能处理路径
        # 前端传来的filename可能是：
        # 1. "weiam/onestrm/..." (完整路径去掉开头斜杠)
        # 2. "nonexistent.jpg" (纯文件名)

        if filename.startswith(media_root.lstrip('/')):
            # 情况1：filename是去掉开头斜杠的完整路径
            # 例如："weiam/onestrm/..." -> "/weiam/onestrm/..."
            full_path = f"/{filename}"
        else:
            # 情况2：filename是相对路径，与媒体根目录组合
            # 例如："nonexistent.jpg" -> "/weiam/nonexistent.jpg"
            full_path = os.path.join(media_root, filename)

        # 添加调试日志
        current_app.logger.debug(f"访问媒体文件: filename={filename}, 完整路径={full_path}, 媒体根路径={media_root}")

        # 检查路径是否在允许的范围内
        if not is_safe_path(full_path):
            current_app.logger.warning(f"尝试访问禁止路径: {full_path}, 媒体根路径: {media_root}")
            return "Forbidden", 403

        # 确保目录和文件名正确提取
        directory = os.path.dirname(full_path)
        name = os.path.basename(full_path)
        
        # 安全检查：确保目录和文件名不含有可能导致路径遍历的内容
        if '..' in directory or '..' in name:
            current_app.logger.warning(f"检测到可能的路径遍历尝试: {directory}/{name}")
            return "Forbidden", 403
        
        # 添加调试日志
        current_app.logger.debug(f"发送文件: 目录={directory}, 文件名={name}")
        
        # 检查文件是否存在
        if not os.path.exists(os.path.join(directory, name)):
            current_app.logger.warning(f"请求的文件不存在: {directory}/{name}")
            return "File not found", 404
        
        return send_from_directory(directory, name, as_attachment=False)
        
    except Exception as e:
        current_app.logger.error(f"处理媒体文件请求时发生错误: {str(e)}", exc_info=True)
        return "Internal Server Error", 500

@media_api.route('/watermarks/<path:filename>')
def serve_watermark_file(filename):
    """
    水印文件服务路由
    处理水印文件的访问请求
    """
    return send_from_directory('/app/assets', filename)
