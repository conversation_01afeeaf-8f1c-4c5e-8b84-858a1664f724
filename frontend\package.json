{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^1.7.13", "@heroicons/react": "^2.0.16", "axios": "^1.3.4", "clsx": "^1.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.2", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.14", "postcss": "^8.4.21", "tailwindcss": "^3.2.7"}}