# API Handler 最终架构文档

## 概述

经过完整的7阶段重构，原本约2000行的单体 `api_handler.py` 文件已成功拆分为9个专业化的API模块，主文件精简至52行。每个模块职责明确，便于维护和扩展。

## 文件架构

### 核心文件
```
backend/
├── api_handler.py                 # 主入口文件 (52行)
├── api/                          # API模块目录
│   ├── __init__.py               # 模块初始化
│   ├── settings_api.py           # 设置管理模块 (185行)
│   ├── file_api.py               # 文件操作模块 (127行)
│   ├── content_api.py            # 内容管理模块 (206行)
│   ├── nfo_api.py                # NFO数据管理模块 (206行)
│   ├── image_api.py              # 图片处理模块 (514行)
│   ├── logs_api.py               # 日志管理模块 (88行)
│   ├── cache_api.py              # 缓存管理模块 (115行)
│   └── metadata_api.py           # 元数据检索模块 (517行)
└── image_processing.py           # 统一图片处理引擎 (368行)
```

## 模块详细说明

### 1. api_handler.py - 主入口文件
**职责**: 模块导入和蓝图注册
**代码行数**: 52行
```python
# 核心功能
- 导入所有API模块
- 注册Flask蓝图
- 统一URL前缀管理
```

### 2. settings_api.py - 设置管理模块
**职责**: 系统配置管理
**代码行数**: 185行
**API路由**:
- `GET /api/settings` - 获取系统设置
- `POST /api/settings` - 更新系统设置

### 3. file_api.py - 文件操作模块  
**职责**: 文件系统操作
**代码行数**: 127行
**API路由**:
- `GET /api/browse-files` - 浏览文件系统
- `GET /api/validate-path` - 验证路径有效性

### 4. content_api.py - 内容管理模块
**职责**: 电影内容和图片状态管理
**代码行数**: 206行
**API路由**:
- `GET /api/latest-items` - 获取最新高画质项目
- `GET /api/low-quality-items` - 获取低画质项目列表
- `POST /api/skip-item/<int:item_id>` - 跳过低画质项目
- `POST /api/refresh-item-images/<int:item_id>` - 刷新项目图片状态

### 5. nfo_api.py - NFO数据管理模块
**职责**: NFO文件处理和电影元数据管理
**代码行数**: 206行
**API路由**:
- `GET /api/manual/find-movie` - 查找电影
- `GET /api/manual/movie-details/<int:movie_id>` - 获取电影详情
- `GET /api/manual/nfo-content/<int:nfo_id>` - 获取NFO内容
- `POST /api/manual/save-nfo/<int:nfo_id>` - 保存NFO内容
- `GET /api/handmade/nfo-details` - 获取手作NFO详情
- `POST /api/handmade/save-nfo` - 保存手作NFO

### 6. image_api.py - 图片处理模块
**职责**: 图片处理、水印添加、封面缓存管理
**代码行数**: 514行
**API路由**:
- `POST /api/process/poster` - 处理封面图片
- `POST /api/process/fanart-and-thumb` - 处理背景图和缩略图
- `POST /api/process/upload-image` - 处理上传图片
- `GET /api/cover-cache` - 获取封面缓存状态
- `POST /api/cover-cache/refresh` - 刷新封面缓存
- `POST /api/cover-cache/clear` - 清理封面缓存

### 7. logs_api.py - 日志管理模块
**职责**: 系统日志查看和管理
**代码行数**: 88行
**API路由**:
- `GET /api/system-logs` - 获取系统日志
- `POST /api/system-logs/clear` - 清除系统日志

### 8. cache_api.py - 缓存管理模块
**职责**: 各类缓存的管理和清理
**代码行数**: 115行
**API路由**:
- `GET /api/cache/status` - 获取缓存状态
- `POST /api/cache/clear-all` - 清除所有缓存
- `POST /api/cache/clear-specific` - 清除特定缓存

### 9. metadata_api.py - 元数据检索模块
**职责**: CID抓取、DMM信息获取、链接验证
**代码行数**: 517行
**API路由**:
- `GET /api/get-dmm-info` - 获取DMM信息
- `GET /api/get-manual-cid-info` - 手动获取CID信息
- `POST /api/verify-links` - 验证链接有效性
- `POST /api/clear-link-cache` - 清除链接验证缓存
- `POST /api/clear-dmm-domain-cache` - 清除DMM域名缓存

## 支撑模块

### image_processing.py - 统一图片处理引擎
**职责**: 提供统一的图片处理能力
**代码行数**: 368行
**核心功能**:
- 图片质量评估和状态判断
- 图片下载和格式转换
- 水印添加和图片裁剪
- 图片保存和路径管理

## 重构成果

### 代码组织优化
- **主文件精简**: 从~2000行减少到52行 (减少97.4%)
- **模块化设计**: 9个专业化模块，职责清晰
- **代码复用**: 统一的图片处理引擎
- **维护性提升**: 每个模块独立，便于调试和扩展

### 功能完整性
- ✅ 保持所有原有功能
- ✅ API接口向后兼容
- ✅ 数据库操作一致性
- ✅ 错误处理机制完善

### 性能优化
- 模块按需加载
- 减少代码重复
- 统一的图片处理逻辑
- 优化的缓存管理

## URL路由映射

### 前缀统一
所有API路由使用统一前缀 `/api/`

### 路由分组
```
设置管理: /api/settings/*
文件操作: /api/browse-files, /api/validate-path
内容管理: /api/latest-items, /api/low-quality-items/*
NFO管理: /api/manual/*, /api/handmade/*
图片处理: /api/process/*
缓存管理: /api/cover-cache/*, /api/cache/*
日志管理: /api/system-logs/*
元数据: /api/get-dmm-info, /api/get-manual-cid-info, /api/verify-links, /api/clear-*-cache
```

## 部署和维护

### Docker配置
- 所有模块打包到统一容器
- 使用docker-compose.yaml进行服务编排
- 支持环境变量配置

### 监控和日志
- 统一的日志格式和级别
- 模块级别的错误追踪
- 性能监控和资源使用统计

### 扩展性
- 新功能可独立添加为新模块
- 现有模块可独立升级
- 支持插件化架构扩展

## 总结

通过7阶段的系统性重构，成功将单体API文件转换为模块化架构，在保持功能完整性的同时，大幅提升了代码的可维护性、可扩展性和可读性。每个模块职责明确，便于团队协作开发和长期维护。
