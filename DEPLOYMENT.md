# JAssistant 部署指南

## 数据库字段统一更新说明

本次更新将 `studio` 字段统一改为 `maker` 字段，以与大部分网站保持一致。

### 主要变更

1. **数据库结构变更**：
   - `studios` 表重命名为 `makers` 表
   - `nfo_studios` 关联表重命名为 `nfo_makers` 表
   - 相关索引和外键约束同步更新

2. **代码变更**：
   - NFO 解析器优先处理 `maker` 字段
   - Webhook 处理器支持 `maker` 字段
   - 前端界面更新为 "制作商 (Maker)"

3. **兼容性**：
   - 保持对旧 `studio` 字段的兼容性
   - 自动将 `studio` 数据迁移到 `maker` 字段

## Docker 部署

### 1. 准备工作

确保系统已安装 Docker 和 Docker Compose：

```bash
# 检查 Docker 版本
docker --version
docker-compose --version
```

### 2. 配置文件修改

编辑 `docker-compose.yml` 文件：

```yaml
services:
  jassistant:
    image: jassistant:latest
    container_name: jassistant
    network_mode: bridge
    restart: always
    ports:
      - "34711:34711"  # 可根据需要修改端口
    volumes:
      # 应用数据目录
      - ./data/logs:/app/logs
      - ./data/db:/app/db
      - ./data/settings:/app/settings
      - ./data/watermarks:/app/assets
      - ./data/cache:/app/data/cache
      - ./data/cover_cache:/app/cover_cache
      # 媒体文件目录 - 请修改为实际路径
      - /path/to/your/media:/media
    environment:
      - TZ=Asia/Shanghai
      - PYTHONUNBUFFERED=1
      # API 配置 - 请修改为实际值
      - CID_API_KEY=your_api_key_here
      - CID_API_URL=http://your-api-server:port/api/get_cid
```

### 3. 目录结构准备

创建必要的数据目录：

```bash
mkdir -p data/{logs,db,settings,watermarks,cache,cover_cache}
```

### 4. 数据库迁移（重要）

如果是从旧版本升级，需要执行数据库迁移：

```bash
# 进入容器执行迁移脚本
docker exec -it jassistant python /app/backend/migrations/migrate_studio_to_maker.py
```

或者在容器启动前，在主机上执行：

```bash
cd backend
python migrations/migrate_studio_to_maker.py
```

### 5. 启动服务

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 6. 访问应用

应用启动后，可通过以下地址访问：

- Web 界面：`http://localhost:34711`
- API 接口：`http://localhost:34711/api`

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `TZ` | 时区设置 | `Asia/Shanghai` |
| `PYTHONUNBUFFERED` | Python 输出缓冲 | `1` |
| `CID_API_KEY` | CID API 密钥 | 需要配置 |
| `CID_API_URL` | CID API 地址 | 需要配置 |

### 目录映射

| 容器路径 | 主机路径 | 说明 |
|----------|----------|------|
| `/app/logs` | `./data/logs` | 应用日志 |
| `/app/db` | `./data/db` | 数据库文件 |
| `/app/settings` | `./data/settings` | 配置文件 |
| `/app/assets` | `./data/watermarks` | 水印资源 |
| `/app/data/cache` | `./data/cache` | 缓存数据 |
| `/app/cover_cache` | `./data/cover_cache` | 封面缓存 |
| `/media` | `/path/to/your/media` | 媒体文件目录 |

## 升级指南

### 从旧版本升级

1. **备份数据**：
   ```bash
   # 备份数据库
   cp data/db/movies.db data/db/movies.db.backup
   
   # 备份配置
   cp -r data/settings data/settings.backup
   ```

2. **停止旧服务**：
   ```bash
   docker-compose down
   ```

3. **更新镜像**：
   ```bash
   docker pull jassistant:latest
   ```

4. **执行数据库迁移**：
   ```bash
   # 方法1：容器内执行
   docker run --rm -v $(pwd)/data/db:/app/db jassistant:latest python /app/backend/migrations/migrate_studio_to_maker.py
   
   # 方法2：主机执行（需要 Python 环境）
   cd backend && python migrations/migrate_studio_to_maker.py
   ```

5. **启动新服务**：
   ```bash
   docker-compose up -d
   ```

### 验证升级

1. 检查服务状态：
   ```bash
   docker-compose ps
   ```

2. 查看日志：
   ```bash
   docker-compose logs -f
   ```

3. 访问 Web 界面确认功能正常

## 故障排除

### 常见问题

1. **端口冲突**：
   - 修改 `docker-compose.yml` 中的端口映射
   - 确保主机端口未被占用

2. **权限问题**：
   ```bash
   # 修复数据目录权限
   sudo chown -R 1000:1000 data/
   ```

3. **数据库迁移失败**：
   - 检查数据库文件权限
   - 查看迁移日志：`migration_studio_to_maker.log`
   - 恢复备份后重新尝试

4. **容器无法启动**：
   - 检查 Docker 日志：`docker logs jassistant`
   - 确认所有必要的目录已创建
   - 检查环境变量配置

### 日志查看

```bash
# 应用日志
docker-compose logs -f

# 系统日志
tail -f data/logs/app.log

# 迁移日志
cat migration_studio_to_maker.log
```

## 注意事项

1. **数据备份**：升级前务必备份数据库和配置文件
2. **端口配置**：确保映射的端口未被占用
3. **媒体路径**：正确配置媒体文件目录映射
4. **API 配置**：根据实际情况配置 CID API 相关参数
5. **权限设置**：确保数据目录有正确的读写权限

## 技术支持

如遇到问题，请：

1. 查看应用日志和系统日志
2. 检查配置文件是否正确
3. 确认网络连接和端口配置
4. 提供详细的错误信息和日志内容
