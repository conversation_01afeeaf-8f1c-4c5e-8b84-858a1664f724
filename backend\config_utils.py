# backend/config_utils.py
import os
import yaml
import logging
from pathlib import Path

# 添加版本信息
VERSION = "1.0.7"

# 定义需要重启才能生效的设置项
RESTART_REQUIRED_SETTINGS = [
    'log_level',           # 日志级别需要重启日志系统
    'notification_enabled', # 通知开关需要重启调度器
    'notification_time',   # 通知时间需要重启调度器
    'media_root',          # 媒体根路径在多处使用，可能被缓存
    # 数据库备份参数（需要重启）
    'backup_enabled',      # 备份开关需要重启调度器
    'backup_time',         # 备份时间需要重启调度器
    'backup_path',         # 备份路径需要重启
    'backup_retention_days', # 备份保留天数需要重启
    # 性能参数（需要重启）
    'performance_mode',    # 性能模式切换需要重启
    'cache_memory_size',   # 内存缓存大小需要重启
    'cache_file_ttl',      # 文件缓存TTL需要重启
    'db_pool_size',        # 数据库连接池大小需要重启
    'worker_threads',      # 工作线程数需要重启
    # 监控参数（需要重启）
    'enable_monitoring',   # 监控开关需要重启监控系统
    'monitor_cpu_threshold',    # CPU告警阈值需要重启
    'monitor_memory_threshold', # 内存告警阈值需要重启
    'monitor_disk_threshold',   # 磁盘告警阈值需要重启
    # 内存管理参数（需要重启）
    'memory_threshold_mb',      # 内存清理阈值需要重启
    'memory_cleanup_interval',  # 内存检查间隔需要重启
    'enable_auto_memory_cleanup' # 自动内存清理开关需要重启
]

# 性能模式预设配置
PERFORMANCE_PRESETS = {
    'low': {
        'cache_memory_size': 200,    # 最小内存缓存
        'cache_file_ttl': 1800,      # 30分钟
        'cache_image_ttl': 43200,    # 12小时
        'batch_size': 50,
        'max_concurrent_tasks': 2,
        'db_pool_size': 3,           # 最小数据库连接池
        'worker_threads': 2,
        # 低配置监控阈值更宽松
        'monitor_cpu_threshold': 95,
        'monitor_memory_threshold': 95,
        'monitor_disk_threshold': 98,
        # 低配置内存管理（更积极的清理）
        'memory_threshold_mb': 120,
        'memory_cleanup_interval': 300,  # 5分钟
        'enable_auto_memory_cleanup': True
    },
    'medium': {
        'cache_memory_size': 1000,   # 适中的内存缓存
        'cache_file_ttl': 7200,      # 2小时
        'cache_image_ttl': 86400,    # 24小时
        'batch_size': 100,
        'max_concurrent_tasks': 5,
        'db_pool_size': 8,           # 适中的数据库连接池
        'worker_threads': 4,
        # 中配置监控阈值适中
        'monitor_cpu_threshold': 90,
        'monitor_memory_threshold': 90,
        'monitor_disk_threshold': 95,
        # 中配置内存管理
        'memory_threshold_mb': 200,
        'memory_cleanup_interval': 600,  # 10分钟
        'enable_auto_memory_cleanup': True
    },
    'high': {
        'cache_memory_size': 2000,   # 较大的内存缓存，但不过度
        'cache_file_ttl': 14400,     # 4小时
        'cache_image_ttl': 259200,   # 3天
        'batch_size': 200,
        'max_concurrent_tasks': 10,
        'db_pool_size': 15,          # 较大但合理的连接池
        'worker_threads': 8,
        # 高配置监控阈值更严格
        'monitor_cpu_threshold': 85,
        'monitor_memory_threshold': 85,
        'monitor_disk_threshold': 90,
        # 高配置内存管理
        'memory_threshold_mb': 300,
        'memory_cleanup_interval': 1800,  # 30分钟
        'enable_auto_memory_cleanup': True
    }
}

def get_settings_file_path():
    """获取配置文件的绝对路径"""
    # 使用绝对路径确保配置文件保存在正确的映射目录
    return os.path.abspath('/app/settings/config.yaml')

def get_settings():
    """
    从YAML文件加载应用设置
    """
    settings_file = get_settings_file_path()

    # 确保settings目录存在
    os.makedirs(os.path.dirname(settings_file), exist_ok=True)
    
    # 默认设置
    default_settings = {
        'version': VERSION,
        
        # --- 需要重启才能生效的设置 ---
        'log_level': 'INFO',
        'notification_enabled': False,
        'notification_time': '09:00',
        'media_root': '/weiam',  # 媒体文件根路径，默认为/weiam
        
        # --- 可以动态生效的设置 ---
        # 通知设置
        'notification_type': 'custom',  # 可选值: custom, telegram
        'telegram_bot_token': '',
        'telegram_chat_id': '',
        'telegram_random_image_api': '',  # 随机图片API的URL，留空则不发送图片
        'notification_api_url': '',
        'notification_route_id': '',

        # 数据库备份设置
        'backup_enabled': True,
        'backup_time': '02:00',
        'backup_path': '/app/backups',
        'backup_retention_days': 7,

        # 主页显示设置
        'latest_movies_count': 24,
        'cover_size': 'medium',
        'homepage_aspect_ratio': '2:3',
        'secure_mode': False,
        
        # 水印处理设置
        'watermark_targets': ['poster', 'thumb'],
        'watermark_scale_ratio': 12,
        # 海报水印位置设置（比例值，基于1032×1468标准尺寸）
        'watermark_poster_horizontal_ratio': 0.0339,  # 横向边距：35px/1032px (基于宽度计算)
        'watermark_poster_vertical_ratio': 0.0068,    # 纵向边距：10px/1468px (基于高度计算)
        'watermark_poster_spacing_ratio': 0.0097,     # 水印间距：10px/1032px (基于宽度计算)
        # 缩略图水印位置设置（比例值，基于2184×1468标准尺寸）
        'watermark_thumb_horizontal_ratio': 0.0160,   # 横向边距：35px/2184px (基于宽度计算)
        'watermark_thumb_vertical_ratio': 0.0068,     # 纵向边距：10px/1468px (基于高度计算)
        'watermark_thumb_spacing_ratio': 0.0046,      # 水印间距：10px/2184px (基于宽度计算)
        'poster_crop_ratio': 1.415,

        # 性能参数设置
        'performance_mode': 'low',  # 可选值: low, medium, high
        'cache_memory_size': 1000,  # 内存缓存大小
        'cache_file_ttl': 3600,     # 文件缓存TTL（秒）
        'cache_image_ttl': 86400,   # 图片缓存TTL（秒）
        'batch_size': 100,          # 批量操作大小
        'max_concurrent_tasks': 5,  # 最大并发任务数
        'db_pool_size': 10,         # 数据库连接池大小
        'worker_threads': 4,        # 工作线程数
        'enable_personal_mode': True,  # 启用个人模式（简化权限）

        # 监控告警阈值设置
        'monitor_cpu_threshold': 90,     # CPU使用率告警阈值（%）
        'monitor_memory_threshold': 90,  # 内存使用率告警阈值（%）
        'monitor_disk_threshold': 95,    # 磁盘使用率告警阈值（%）
        'enable_monitoring': True,       # 是否启用监控告警

        # 内存管理设置
        'memory_threshold_mb': 150,      # 内存清理阈值（MB）
        'memory_cleanup_interval': 300,  # 内存检查间隔（秒）
        'enable_auto_memory_cleanup': True,  # 启用自动内存清理
        
        # 图片质量判断标准
        'high_quality_min_height': 800,  # 最小高度
        'high_quality_min_width': 450,   # 最小宽度
        'high_quality_min_size_kb': 50   # 最小文件大小（KB）
    }
    
    # 如果设置文件存在，则加载
    if os.path.exists(settings_file):
        try:
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = yaml.safe_load(f)
            
            # 确保添加了版本号
            settings['version'] = VERSION
                
            # 如果加载的是None (空文件)，使用默认设置
            if not settings:
                settings = default_settings
            
            # 确保类型一致性
            _normalize_settings_types(settings, default_settings)
            
        except Exception as e:
            logging.error(f"读取设置文件失败: {e}")
            settings = default_settings
    else:
        # 文件不存在，使用默认设置并创建文件
        settings = default_settings
        save_settings(settings)
    
    return settings

def _normalize_settings_types(settings, default_settings):
    """
    确保设置值的类型与默认值一致
    
    Args:
        settings: 当前设置
        default_settings: 默认设置，用于参考类型
    """
    # 确保数值类型一致
    for key, default_value in default_settings.items():
        if key in settings:
            # 跳过不需要转换的值
            if key in ['watermark_targets', 'version', 'notification_time']:
                continue
                
            try:
                # 布尔值处理
                if isinstance(default_value, bool):
                    if isinstance(settings[key], str):
                        settings[key] = settings[key].lower() in ['true', 'yes', '1', 'on']
                    else:
                        settings[key] = bool(settings[key])
                # 整数处理
                elif isinstance(default_value, int):
                    settings[key] = int(settings[key])
                # 浮点数处理
                elif isinstance(default_value, float):
                    settings[key] = float(settings[key])
            except (ValueError, TypeError):
                # 如果转换失败，使用默认值
                logging.warning(f"设置项 '{key}' 的值 '{settings[key]}' 类型错误，使用默认值 '{default_value}'")
                settings[key] = default_value

def save_settings(settings, old_settings=None):
    """
    保存设置到YAML文件
    
    Args:
        settings: 新的设置
        old_settings: 旧的设置，用于比较哪些设置已更改
        
    Returns:
        (success, message, restart_needed): 
            - success: 是否保存成功
            - message: 操作消息
            - restart_needed: 是否需要重启生效
    """
    settings_file = get_settings_file_path()
    
    # 确保设置包含版本信息
    settings['version'] = VERSION
    
    # 检查是否需要重启
    restart_needed = False
    if old_settings:
        # 检查每一个需要重启的设置项是否被修改
        for key in RESTART_REQUIRED_SETTINGS:
            if key in old_settings and key in settings:
                # 转换为字符串进行比较，避免类型差异导致误判
                old_val_str = str(old_settings.get(key))
                new_val_str = str(settings.get(key))
                if old_val_str != new_val_str:
                    logging.info(f"检测到需要重启的设置已更改: {key} 从 {old_val_str} 改为 {new_val_str}")
                    restart_needed = True
                    break
    else:
        # 如果没有提供旧设置，默认不需要重启
        restart_needed = False
    
    try:
        # 确保settings目录存在
        os.makedirs(os.path.dirname(settings_file), exist_ok=True)
        
        with open(settings_file, 'w', encoding='utf-8') as f:
            yaml.dump(settings, f, default_flow_style=False, allow_unicode=True)
            
        msg = "设置已保存"
        if restart_needed:
            msg += "，部分设置需要重启容器才能生效"
        
        return True, msg, restart_needed
    except Exception as e:
        logging.error(f"保存设置失败: {e}")
        return False, f"保存设置失败: {e}", False
        
def is_restart_required(key):
    """
    检查指定的设置项是否需要重启才能生效
    
    Args:
        key: 设置项的键名
        
    Returns:
        bool: 是否需要重启
    """
    return key in RESTART_REQUIRED_SETTINGS

def get_restart_required_settings():
    """
    获取所有需要重启才能生效的设置项列表

    Returns:
        list: 设置项键名列表
    """
    return RESTART_REQUIRED_SETTINGS

def apply_performance_preset(settings, mode):
    """
    应用性能模式预设配置

    Args:
        settings: 当前设置字典
        mode: 性能模式 ('low', 'medium', 'high')

    Returns:
        更新后的设置字典
    """
    if mode not in PERFORMANCE_PRESETS:
        return settings

    preset = PERFORMANCE_PRESETS[mode]
    settings.update(preset)
    settings['performance_mode'] = mode

    return settings

def get_performance_presets():
    """
    获取性能预设配置
    """
    return PERFORMANCE_PRESETS
