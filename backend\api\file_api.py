# backend/api/file_api.py
"""
文件操作管理API模块
负责处理文件和目录的操作，包括列表、删除、重命名、创建等功能
"""
import os
import logging
import signal
from flask import Blueprint, request, jsonify, current_app
from api.media_api import get_media_root, is_safe_path
from utils import safe_rename, safe_delete, safe_copy, get_safe_filename, ensure_dir_exists
from config_utils import get_settings
import time

logger = logging.getLogger(__name__)

# 创建文件操作API蓝图
file_api = Blueprint('file_api', __name__)

@file_api.route('/files/list', methods=['GET'])
def list_files():
    """
    获取文件列表API
    支持分页、筛选和简单模式
    """
    req_path = request.args.get('path', get_media_root())
    
    # 处理请求路径
    if not req_path:
        req_path = get_media_root()
    
    # 安全检查
    if not is_safe_path(req_path):
        current_app.logger.warning(f"拒绝访问路径: {req_path}, 媒体根路径: {get_media_root()}")
        return jsonify({"error": "禁止访问的路径", "details": f"请求路径: {req_path}, 媒体根路径: {get_media_root()}"}), 403
    
    # 确保路径存在
    if not os.path.exists(req_path):
        return jsonify({"error": "路径不存在", "path": req_path}), 404
    
    # 确保是目录
    if not os.path.isdir(req_path):
        return jsonify({"error": "不是有效目录", "path": req_path}), 400
    
    # 获取分页参数
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 200))
    simple_mode = request.args.get('simple', 'false').lower() == 'true'
    
    # 获取文件类型筛选器
    file_type_filters = request.args.getlist('file_types')
    
    try:
        # 设置超时，避免大目录处理时间过长
        def timeout_handler(signum, frame):
            _ = signum, frame  # 忽略未使用的参数
            raise TimeoutError("处理目录内容超时，目录可能包含太多文件")
        
        # 设置30秒超时
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(30)
        
        try:
            # 获取目录内所有项
            all_names = os.listdir(req_path)
            
            # 重置超时计时器
            signal.alarm(0)
        except TimeoutError as e:
            current_app.logger.warning(f"目录列表获取超时: {req_path}")
            return jsonify({"error": str(e)}), 504  # Gateway Timeout
        
        # 应用筛选器
        if file_type_filters:
            filtered_names = []
            for name in all_names:
                ext = os.path.splitext(name)[1].lower()
                # 如果没有扩展名但需要显示目录
                if (not ext and os.path.isdir(os.path.join(req_path, name)) and 'dir' in file_type_filters) or \
                   (ext and ext[1:] in file_type_filters):
                    filtered_names.append(name)
            all_names = filtered_names
        
        # 排序
        all_names.sort()
        
        # 分页处理
        total_items = len(all_names)
        total_pages = (total_items + page_size - 1) // page_size
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        page_items = all_names[start_idx:end_idx]
        
        items = []
        
        # 在简单模式下，只获取最基本的文件信息
        if simple_mode:
            for name in page_items:
                item_abs_path = os.path.join(req_path, name)
                try:
                    is_dir = os.path.isdir(item_abs_path)
                    items.append({
                        "name": name,
                        "path": item_abs_path,
                        "is_directory": is_dir,
                        "size": 0,  # 简单模式不获取大小
                        "modified_at": 0  # 简单模式不获取修改时间
                    })
                except (FileNotFoundError, PermissionError):
                    # 跳过无权限或丢失的文件
                    continue
        else:
            # 标准模式，获取更多文件详情
            for name in page_items:
                item_abs_path = os.path.join(req_path, name)
                try:
                    stat = os.stat(item_abs_path)
                    is_dir = os.path.isdir(item_abs_path)
                    
                    # 对于目录，只获取必要信息，不递归统计大小
                    items.append({
                        "name": name,
                        "path": item_abs_path,
                        "is_directory": is_dir,
                        "size": 0 if is_dir else stat.st_size,
                        "modified_at": stat.st_mtime
                    })
                except (FileNotFoundError, PermissionError):
                    # 跳过无权限或丢失的文件
                    continue
                
        return jsonify({
            "items": items,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_items": total_items,
                "total_pages": total_pages
            }
        })
    except FileNotFoundError:
        return jsonify({"error": "目录未找到"}), 404
    except PermissionError:
        return jsonify({"error": "没有权限访问该目录"}), 403
    except TimeoutError as e:
        return jsonify({"error": str(e)}), 504  # Gateway Timeout
    except Exception as e:
        current_app.logger.error(f"获取文件列表失败: {e}", exc_info=True)
        return jsonify({"error": str(e)}), 500
    finally:
        # 确保超时信号被重置
        if 'signal' in locals():
            try:
                signal.alarm(0)
            except:
                pass

@file_api.route('/files/rename', methods=['POST'])
def rename_file():
    """
    重命名文件或目录API
    """
    old_path = request.json.get('path')
    new_name = request.json.get('new_name')
    
    if not all([old_path, new_name]):
        return jsonify({"error": "缺少必要参数"}), 400
    
    if not is_safe_path(old_path):
        return jsonify({"error": "无效的请求路径"}), 400
    
    new_path = os.path.join(os.path.dirname(old_path), new_name)
    if not is_safe_path(new_path):
        return jsonify({"error": "无效的新路径"}), 400
    
    try:
        success, error = safe_rename(old_path, new_path)
        if success:
            return jsonify({"success": True, "message": "重命名成功"})
        else:
            return jsonify({"error": error}), 500
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@file_api.route('/files/delete', methods=['POST'])
def delete_files():
    """
    删除文件或目录API
    支持批量删除
    """
    paths = request.json.get('paths', [])
    if not paths:
        return jsonify({"error": "没有提供要删除的路径"}), 400
    
    for path in paths:
        if not is_safe_path(path):
            return jsonify({"error": f"禁止删除路径: {path}"}), 403
        
        try:
            success, error = safe_delete(path)
            if not success:
                return jsonify({"error": error}), 500
        except Exception as e:
            return jsonify({"error": f"删除 {path} 失败: {e}"}), 500
    
    return jsonify({"success": True, "message": "删除成功"})

@file_api.route('/files/create-dir', methods=['POST'])
def create_directory():
    """
    创建目录API
    """
    parent_path = request.json.get('path')
    name = request.json.get('name')

    if not all([parent_path, name]):
        return jsonify({"error": "缺少必要参数"}), 400

    if not is_safe_path(parent_path):
        return jsonify({"error": "无效的父目录路径"}), 400

    new_dir_path = os.path.join(parent_path, name)
    if not is_safe_path(new_dir_path):
        return jsonify({"error": "无效的新目录路径"}), 400

    try:
        os.makedirs(new_dir_path, exist_ok=True)
        return jsonify({"success": True, "message": "目录创建成功"})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@file_api.route('/files/move', methods=['POST'])
def move_file():
    """
    移动文件或目录API
    支持移动到不同目录
    """
    source_path = request.json.get('source_path')
    dest_path = request.json.get('dest_path')

    if not all([source_path, dest_path]):
        return jsonify({"error": "缺少必要参数"}), 400

    if not is_safe_path(source_path):
        return jsonify({"error": "无效的源路径"}), 400

    if not is_safe_path(dest_path):
        return jsonify({"error": "无效的目标路径"}), 400

    if not os.path.exists(source_path):
        return jsonify({"error": "源文件或目录不存在"}), 404

    try:
        # 如果目标路径是目录，则将源文件移动到该目录下
        if os.path.isdir(dest_path):
            dest_path = os.path.join(dest_path, os.path.basename(source_path))

        # 确保目标路径仍然安全
        if not is_safe_path(dest_path):
            return jsonify({"error": "计算后的目标路径无效"}), 400

        # 使用重命名实现移动（在同一文件系统内）
        success, error = safe_rename(source_path, dest_path)
        if success:
            return jsonify({"success": True, "message": "移动成功"})
        else:
            # 如果重命名失败，尝试复制+删除
            success_copy, error_copy = safe_copy(source_path, dest_path)
            if success_copy:
                success_del, error_del = safe_delete(source_path)
                if success_del:
                    return jsonify({"success": True, "message": "移动成功"})
                else:
                    return jsonify({"error": f"复制成功但删除源文件失败: {error_del}"}), 500
            else:
                return jsonify({"error": f"移动失败: {error_copy}"}), 500
    except Exception as e:
        return jsonify({"error": str(e)}), 500


