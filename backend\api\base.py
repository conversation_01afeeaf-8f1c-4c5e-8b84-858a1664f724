# backend/api/base.py
"""
基础API类和装饰器
"""
from flask import Blueprint, current_app
from functools import wraps
import logging

def log_api_call(func):
    """API调用日志装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            current_app.logger.debug(f"API调用: {func.__name__}")
            return func(*args, **kwargs)
        except Exception as e:
            current_app.logger.error(f"API调用失败 {func.__name__}: {e}", exc_info=True)
            raise
    return wrapper

class BaseAPI:
    """基础API类"""
    
    def __init__(self, name, import_name, url_prefix=None):
        self.blueprint = Blueprint(name, import_name, url_prefix=url_prefix)
        self.logger = logging.getLogger(name)
    
    def route(self, rule, **options):
        """路由装饰器"""
        return self.blueprint.route(rule, **options)
    
    def register_routes(self):
        """注册路由的抽象方法，子类需要实现"""
        raise NotImplementedError("子类必须实现register_routes方法")
