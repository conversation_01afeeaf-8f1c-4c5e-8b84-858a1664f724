@tailwind base;
@tailwind components;
@tailwind utilities;

/* 定义CSS变量以支持主题切换 */
@layer base {
  :root {
    /* 亮色主题颜色 */
    --color-primary-bg: #F9FAFB; /* gray-50 */
    --color-secondary-bg: #FFFFFF;
    --color-sidebar-bg: #F3F4F6; /* gray-100 */
    --color-primary-accent: #2563EB; /* blue-600 */
    --color-secondary-accent: #059669; /* emerald-600 */
    --color-primary-text: #111827; /* gray-900 */
    --color-secondary-text: #4B5563; /* gray-600 */
    --color-danger: #DC2626; /* red-600 */
    --color-warning: #F59E0B; /* amber-500 */
    --color-border: #E5E7EB; /* gray-200 */
  }

  .dark {
    /* 暗色主题颜色 (覆盖 :root) */
    --color-primary-bg: #10101A;
    --color-secondary-bg: #181828;
    --color-sidebar-bg: #141420;
    --color-primary-accent: #3B82F6; /* blue-500 */
    --color-secondary-accent: #2CB67D;
    --color-primary-text: #FFFFFE;
    --color-secondary-text: #94A1B2;
    --color-danger: #EF4444;
    --color-warning: #F59E0B;
    --color-border: #374151; /* gray-700 */
  }

  body {
    background-color: var(--color-primary-bg);
    color: var(--color-primary-text);
    @apply transition-colors duration-300;
  }
}

/* 定义可重用的组件样式 */
@layer components {
  .input-field {
    @apply block w-full rounded-md shadow-sm p-2 transition-colors duration-200;
    background-color: var(--color-secondary-bg);
    border: 1px solid var(--color-border);
    color: var(--color-primary-text);
  }
  .input-field:focus {
    --tw-ring-color: var(--color-primary-accent);
    border-color: var(--color-primary-accent);
    @apply ring-1;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar { width: 8px; }
::-webkit-scrollbar-track { background-color: var(--color-secondary-bg); }
::-webkit-scrollbar-thumb { background-color: var(--color-primary-accent); @apply rounded-md; }
