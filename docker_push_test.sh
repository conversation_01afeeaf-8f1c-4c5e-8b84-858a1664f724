#!/bin/bash
# 构建并推送测试、Beta或Alpine版本的Docker镜像到Docker Hub
#
# 使用示例:
#   ./docker_push_test.sh test           # 推送test标签 (使用标准Dockerfile)
#   ./docker_push_test.sh beta 1.0.5     # 推送beta和1.0.5-beta标签 (使用标准Dockerfile)
#   ./docker_push_test.sh alpine         # 推送alpine标签 (使用Dockerfile.alpine)
#   ./docker_push_test.sh alpine 1.0.5   # 推送alpine和1.0.5-alpine标签 (使用Dockerfile.alpine)

# 配置
USERNAME="aliez0lie1"
IMAGE_NAME="jassistant"
VERSION=""  # 默认版本号
TAG_TYPE=""
USE_VERSION=false  # 是否使用版本号标签
DOCKERFILE="Dockerfile"  # 默认Dockerfile

# 处理命令行参数
if [ -z "$1" ]; then
  echo "错误: 请指定要推送的版本类型 (test、beta 或 alpine)"
  echo "用法: $0 [test|beta|alpine] [版本号，可选]"
  exit 1
elif [ "$1" = "test" ] || [ "$1" = "beta" ] || [ "$1" = "alpine" ]; then
  TAG_TYPE="$1"
  # 如果是alpine版本，使用Alpine Dockerfile
  if [ "$1" = "alpine" ]; then
    DOCKERFILE="Dockerfile.alpine"
  fi
else
  echo "错误: 无效的版本类型。请使用 'test'、'beta' 或 'alpine'"
  echo "用法: $0 [test|beta|alpine] [版本号，可选]"
  exit 1
fi

# 检查是否提供了版本号参数
if [ -n "$2" ]; then
  VERSION="$2"
  USE_VERSION=true
  echo "将使用指定的版本号: $VERSION"
else
  # 如果没有提供版本号，则不创建带版本号的标签
  USE_VERSION=false
  echo "未指定版本号，将只创建 $TAG_TYPE 标签"
fi

# 构建标签名称
TAG_IMAGE_NAME="$USERNAME/$IMAGE_NAME:$TAG_TYPE"
if [ "$USE_VERSION" = true ]; then
  VERSIONED_TAG_NAME="$USERNAME/$IMAGE_NAME:$VERSION-$TAG_TYPE"
fi

echo "===== 开始构建和推送 $TAG_TYPE 版本的Docker镜像 ====="
echo "使用Dockerfile: $DOCKERFILE"
echo "标签名称: $TAG_IMAGE_NAME"
if [ "$USE_VERSION" = true ]; then
  echo "带版本号的标签: $VERSIONED_TAG_NAME"
fi

# 确保已经登录Docker Hub
echo "正在检查Docker登录状态..."
if ! docker info | grep -q "Username"; then
  echo "您尚未登录Docker Hub，请先登录:"
  docker login
fi

# 构建Docker镜像
echo "正在构建Docker镜像..."
docker build -f $DOCKERFILE -t $TAG_IMAGE_NAME .

# 如果指定了版本号，也创建带版本的标签
if [ "$USE_VERSION" = true ]; then
  docker tag $TAG_IMAGE_NAME $VERSIONED_TAG_NAME
fi

# 推送到Docker Hub
echo "正在推送镜像到Docker Hub..."
docker push $TAG_IMAGE_NAME

if [ "$USE_VERSION" = true ]; then
  docker push $VERSIONED_TAG_NAME
  echo "已推送带版本号的标签: $VERSIONED_TAG_NAME"
fi

echo "===== 完成! ====="
echo "已成功推送 $TAG_TYPE 标签: $TAG_IMAGE_NAME"
echo "您现在可以在Docker Hub上查看您的镜像: https://hub.docker.com/r/$USERNAME/$IMAGE_NAME" 