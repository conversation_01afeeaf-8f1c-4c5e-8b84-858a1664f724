# Jassistant - 媒体文件助手

![Jassistant Logo](https://raw.githubusercontent.com/fishinsevens/Jassistant/main/logo.png)

## 项目简介

Jassistant是一款专为JAV媒体的工具，通过webhook获取EMBY最新媒体首页显示，可以查找高清替换，支持水印处理，NFO编辑等功能。

数据清洗只支持 通过 EMBY webhook 获取后写入数据库的影片

未写入数据库的影片在文件管理中双击 NFO 使用手作修正

## 版本历史

- **1.0.0** - 初始版本也是~~最终版本~~
- **1.0.1** - 新增TG机器人通知
- **1.0.2** - 解除后台限制媒体库根目录，适应不同 EMBY 映射关系
- **1.0.3** - 修复日志显示，增加首页图像缓存等
- **1.0.4** - DMM链接验证缓存，避免重复验证已确认存在的图片，一些修复
- **1.0.5** - 远程图片预加载缓存，提升图片处理速度，一些修改
- **1.0.6** - 修复通知，每日通知正常工作，新增批量入库，水印根据图片大小调整位置
- **1.0.6.1** - 修复批量入库完成后再次点击开始批量导入会默认整个媒体库
- **1.0.7** - 番号-CID规则管理从其他容器API改为本地管理API，修改数据库存储将studio字段统一改为maker字段，以与大部分网站保持一致（使用命令迁移或删除原数据库），新增数据库备份计划任务

- **梦里计划** - ~~批量元数据入库~~/修改，批量图片下载/替换等

## BUG反馈
- TG@四五声的通知

## 功能特点

- **最新入库展示**：通过webhook获取EMBY最新添加的媒体
- **高清替换**：查找并替换低画质媒体封面
- **数据清洗**：修正数据库中的媒体信息
- **手作修正**：修改未在数据库中的NFO文件
- **批量入库**：批量导入NFO文件和图片信息到数据库
- **每日通知**：支持自定义通知和Telegram机器人通知
- **规则管理**：番号-CID规则管理

## 技术栈

- **后端**：Python + Flask
- **前端**：React + Tailwind CSS
- **数据库**：SQLite
- **容器化**：Docker

## 快速开始

### 使用Docker Compose

#### 标准版本
```yaml
services:
  jassistant:
    image: aliez0lie1/jassistant:latest
    container_name: jassistant
    ports:
      - "34711:34711"
    volumes:
      - ./data/logs:/app/logs
      - ./data/db:/app/db
      - ./data/backups:/app/backups  # 数据库备份存储目录
      - ./data/settings:/app/settings
      - ./data/watermarks:/app/assets
      - ./data/cache:/app/data/cache  # 整个缓存目录
      #- ./data/images:/app/data/cache/images  # 或者只映射图片缓存
      - ./data/cover_cache:/app/cover_cache  # 封面缓存目录
      - /your/media/path:/weiam
    environment:
      - TZ=Asia/Shanghai
      # 设置Python环境变量
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
```

#### Alpine优化版本
```yaml
services:
  jassistant:
    image: aliez0lie1/jassistant:alpine
    container_name: jassistant
    ports:
      - "34711:34711"
    volumes:
      - ./data/logs:/app/logs
      - ./data/db:/app/db
      - ./data/backups:/app/backups  # 数据库备份存储目录
      - ./data/settings:/app/settings
      - ./data/watermarks:/app/assets
      - ./data/cache:/app/data/cache  # 映射缓存目录
      #- ./data/images:/app/data/cache/images  # 或者映射图片缓存目录
      - ./data/cover_cache:/app/cover_cache  # 封面缓存目录
      - /your/media/path:/weiam
    environment:
      - TZ=Asia/Shanghai
      # 设置Python环境变量
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
```

## 配置说明

### 卷挂载

| 挂载点 | 描述 |
| --- | --- |
| /app/logs | 日志文件 |
| /app/db | 数据库文件 |
| /app/backups | 数据库备份文件 |
| /app/settings | 设置文件 |
| /app/assets | 水印资源文件 |
| /app/data/cache | 缓存目录 |
| /app/cover_cache| 首页图像缓存 |
| /weiam | 媒体文件目录，可以修改为其他路径名，但需要在设置页面中相应更新"媒体根路径"设置，与EMBY媒体库保持一致映射 |

### 水印资源文件请自备喜欢的

命名如下：

4K：4k.png

8K：8k.png

字幕：subs.png

破解：cracked.png

流出：leaked.png

有码：mosaic.png

无码：uncensored.png

### EMBY 通知设置

http://localhost:34711/api/webhook

application/json

勾选媒体库-新媒体已添加

### 每日通知功能

支持两种通知方式：

#### 1. 自定义通知
- 配置API URL和Route ID
- 支持任何接受JSON POST请求的通知服务

#### 2. Telegram机器人通知
- 配置Bot Token和Chat ID
- 支持随机图片API，让通知更生动
- 支持Markdown格式消息

通知功能可在系统设置页面进行配置，支持设定每日通知时间，自动统计昨天的入库情况。

## 浏览器访问

启动容器后，打开浏览器访问：`http://localhost:34711`