# Dockerfile.alpine - 优化版本

# --- Stage 1: Build React Frontend ---
FROM node:18-alpine AS build-stage
WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm install
COPY frontend/ ./
RUN npm run build

# --- Stage 2: Build Python Backend (Alpine优化版) ---
FROM python:3.10-alpine
WORKDIR /app

ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# 安装必要的系统依赖（Alpine版本）
RUN apk add --no-cache \
    jpeg-dev \
    zlib-dev \
    gcc \
    musl-dev \
    linux-headers \
    supervisor \
    && rm -rf /var/cache/apk/*

# 复制和安装Python依赖（Alpine兼容版本）
COPY backend/requirements.alpine.txt .
RUN pip install --no-cache-dir -r requirements.alpine.txt

# 复制后端代码
COPY backend/ .

# 复制前端构建文件
COPY --from=build-stage /app/frontend/build ./static

# 复制现有的supervisor配置
COPY supervisord.conf /etc/supervisor/conf.d/

EXPOSE 34711

# 使用现有的supervisor配置
CMD ["/usr/bin/supervisord", "-n", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
