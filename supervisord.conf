[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
pidfile=/var/run/supervisord.pid

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[program:web]
command=gunicorn --workers 1 -k gevent --worker-connections 2000 --timeout 120 --max-requests 1000 --max-requests-jitter 50 --bind 0.0.0.0:34711 app:app
directory=/app
user=root
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
environment=PYTHONUNBUFFERED=1

[program:scheduler]
command=python scheduler_standalone.py
directory=/app
user=root
autostart=true
autorestart=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
environment=PYTHONUNBUFFERED=1 
