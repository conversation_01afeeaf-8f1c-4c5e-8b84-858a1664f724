# backend/api/cid_api.py
"""
CID规则管理API
"""
import logging
from flask import Blueprint, request, jsonify
from dao.cid_rule_dao import cid_rule_dao, cid_variant_dao
from services.cid_service import cid_service
from api.metadata_api import scrape_cid_and_maker, scrape_cid_and_maker_with_matching
import re

logger = logging.getLogger(__name__)
cid_api = Blueprint('cid_api', __name__)

# CID规则表现在在主数据库初始化时创建，不需要单独的初始化函数

# 番号正则表达式
BANGOU_REGEX = re.compile(r'^([A-Z]+)-?(\d+)$')

def derive_rule_from_cid(bangou: str, correct_cid: str) -> dict:
    """从番号和其对应的CID中推导出转换规则（前缀、补位、后缀等）"""
    match_bangou = BANGOU_REGEX.match(bangou.upper())
    if not match_bangou:
        raise ValueError(f"无法从番号 '{bangou}' 中解析出系列和数字。")

    series_name_upper = match_bangou.group(1)

    # Handle dummy cid case for modal lookup
    if correct_cid.lower() == 'dummycid':
        return {
            "series_name": series_name_upper,
            "prefix": "",
            "series_name_in_cid": series_name_upper.lower().replace('-', '').replace('_', ''),
            "padding": 0,
            "suffix": ""
        }

    correct_cid_lower = correct_cid.lower()
    series_name_for_splitting = series_name_upper.lower().replace('-', '').replace('_', '')

    if series_name_for_splitting not in correct_cid_lower:
        raise ValueError(f"CID '{correct_cid}' 中未找到系列名 '{series_name_for_splitting}'")

    parts = correct_cid_lower.split(series_name_for_splitting)
    new_prefix = parts[0]
    numeric_part_and_suffix = parts[1]

    num_match = re.match(r'(\d+)(.*)', numeric_part_and_suffix)
    if not num_match:
        raise ValueError(f"无法从CID的后半部分 '{numeric_part_and_suffix}' 中解析出数字")

    new_padding = len(num_match.group(1))
    new_suffix = num_match.group(2)

    return {
        "series_name": series_name_upper,
        "prefix": new_prefix,
        "series_name_in_cid": series_name_for_splitting,
        "padding": new_padding,
        "suffix": new_suffix
    }

# ==================== 主规则API ====================

@cid_api.route('/cid/main-rules', methods=['GET'])
def get_main_rules():
    """获取主规则列表"""
    try:

        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 15, type=int)
        search = request.args.get('search', '', type=str)

        offset = (page - 1) * per_page
        
        # 获取规则列表
        rules = cid_rule_dao.find_all_with_variant_count(search, per_page, offset)
        total = cid_rule_dao.count_with_search(search)
        
        return jsonify({
            "success": True,
            "data": rules,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total,
                "pages": (total + per_page - 1) // per_page
            }
        })
        
    except Exception as e:
        logger.error(f"获取主规则列表失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"获取主规则列表失败: {str(e)}"}), 500

@cid_api.route('/cid/main-rules', methods=['POST'])
def create_main_rule():
    """创建主规则"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['series_name', 'number_padding']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"缺少必填字段: {field}"}), 400
        
        # 数据验证和准备
        try:
            number_padding = int(data['number_padding'])
            if number_padding < 0 or number_padding > 10:
                return jsonify({"success": False, "message": "补位规则必须在0-10之间"}), 400
        except (ValueError, TypeError):
            return jsonify({"success": False, "message": "补位规则必须是有效数字"}), 400

        series_name = data['series_name'].upper().strip()
        if not series_name:
            return jsonify({"success": False, "message": "番号前缀不能为空"}), 400

        rule_data = {
            'series_name': series_name,
            'cid_prefix': data.get('cid_prefix', '').strip(),
            'series_name_in_cid': data.get('series_name_in_cid', '').strip(),
            'number_padding': number_padding,
            'suffix': data.get('suffix', '').strip(),
            'description': data.get('description', '').strip(),
            'status': data.get('status', '已确认')
        }
        
        # 检查是否已存在相同的番号前缀
        existing_rule = cid_rule_dao.find_by_series_name(rule_data['series_name'])
        if existing_rule:
            return jsonify({"success": False, "message": f"番号前缀 '{rule_data['series_name']}' 已存在，请使用不同的番号前缀"}), 400

        # 插入数据
        rule_id = cid_rule_dao.insert(rule_data)

        if rule_id:
            return jsonify({"success": True, "message": "主规则创建成功", "id": rule_id})
        else:
            return jsonify({"success": False, "message": "主规则创建失败，请检查数据格式"}), 500

    except Exception as e:
        logger.error(f"创建主规则失败: {e}", exc_info=True)
        if "UNIQUE constraint failed" in str(e) or "series_name" in str(e):
            return jsonify({"success": False, "message": f"番号前缀已存在，请使用不同的番号前缀"}), 400
        else:
            return jsonify({"success": False, "message": f"创建主规则失败: {str(e)}"}), 500

@cid_api.route('/cid/main-rules/<int:rule_id>', methods=['GET'])
def get_main_rule(rule_id):
    """获取单个主规则详情"""
    try:
        rule = cid_rule_dao.find_by_id(rule_id)
        if not rule:
            return jsonify({"success": False, "message": "主规则不存在"}), 404
        
        return jsonify({"success": True, "data": rule})
        
    except Exception as e:
        logger.error(f"获取主规则详情失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"获取主规则详情失败: {str(e)}"}), 500

@cid_api.route('/cid/main-rules/<int:rule_id>', methods=['PUT'])
def update_main_rule(rule_id):
    """更新主规则"""
    try:
        data = request.get_json()
        
        # 验证规则是否存在
        existing_rule = cid_rule_dao.find_by_id(rule_id)
        if not existing_rule:
            return jsonify({"success": False, "message": "主规则不存在"}), 404
        
        # 准备更新数据
        update_data = {}
        allowed_fields = ['series_name', 'cid_prefix', 'series_name_in_cid', 'number_padding', 'suffix', 'description', 'status']
        
        for field in allowed_fields:
            if field in data:
                if field == 'series_name':
                    update_data[field] = data[field].upper().strip()
                elif field == 'number_padding':
                    update_data[field] = int(data[field])
                else:
                    update_data[field] = data[field]
        
        # 更新数据
        success = cid_rule_dao.update(rule_id, update_data)
        
        if success:
            return jsonify({"success": True, "message": "主规则更新成功"})
        else:
            return jsonify({"success": False, "message": "主规则更新失败"}), 500
            
    except Exception as e:
        logger.error(f"更新主规则失败: {e}", exc_info=True)
        if "UNIQUE constraint failed" in str(e):
            return jsonify({"success": False, "message": "番号前缀已存在"}), 409
        return jsonify({"success": False, "message": f"更新主规则失败: {str(e)}"}), 500

@cid_api.route('/cid/main-rules/<int:rule_id>', methods=['DELETE'])
def delete_main_rule(rule_id):
    """删除主规则"""
    try:
        # 验证规则是否存在
        existing_rule = cid_rule_dao.find_by_id(rule_id)
        if not existing_rule:
            return jsonify({"success": False, "message": "主规则不存在"}), 404
        
        # 删除规则（级联删除变体规则）
        success = cid_rule_dao.delete(rule_id)
        
        if success:
            return jsonify({"success": True, "message": "主规则删除成功"})
        else:
            return jsonify({"success": False, "message": "主规则删除失败"}), 500
            
    except Exception as e:
        logger.error(f"删除主规则失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"删除主规则失败: {str(e)}"}), 500

# ==================== 变体规则API ====================

@cid_api.route('/cid/main-rules/<int:main_rule_id>/variants', methods=['GET'])
def get_variant_rules(main_rule_id):
    """获取主规则的变体规则列表"""
    try:
        # 验证主规则是否存在
        main_rule = cid_rule_dao.find_by_id(main_rule_id)
        if not main_rule:
            return jsonify({"success": False, "message": "主规则不存在"}), 404
        
        # 获取变体规则
        variants = cid_variant_dao.find_by_main_rule_id(main_rule_id)
        
        return jsonify({
            "success": True,
            "main_rule": main_rule,
            "variants": variants
        })

    except Exception as e:
        logger.error(f"获取变体规则列表失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"获取变体规则列表失败: {str(e)}"}), 500

@cid_api.route('/cid/main-rules/<int:main_rule_id>/variants', methods=['POST'])
def create_variant_rule(main_rule_id):
    """为主规则添加变体规则"""
    try:
        # 验证主规则是否存在
        main_rule = cid_rule_dao.find_by_id(main_rule_id)
        if not main_rule:
            return jsonify({"success": False, "message": "主规则不存在"}), 404

        data = request.get_json()

        # 验证必填字段
        required_fields = ['number_padding']
        for field in required_fields:
            if field not in data:
                return jsonify({"success": False, "message": f"缺少必填字段: {field}"}), 400

        # 数据验证
        try:
            number_padding = int(data['number_padding'])
            if number_padding < 0 or number_padding > 10:
                return jsonify({"success": False, "message": "补位规则必须在0-10之间"}), 400
        except (ValueError, TypeError):
            return jsonify({"success": False, "message": "补位规则必须是有效数字"}), 400

        try:
            maker_enabled = int(data.get('maker_enabled', 0))
            if maker_enabled not in [0, 1]:
                return jsonify({"success": False, "message": "制作商开关必须是0或1"}), 400
        except (ValueError, TypeError):
            return jsonify({"success": False, "message": "制作商开关必须是有效数字"}), 400

        # 获取下一个优先级（从0开始）
        max_priority = cid_variant_dao.get_max_priority(main_rule_id)

        # 准备数据
        variant_data = {
            'main_rule_id': main_rule_id,
            'maker': data.get('maker', '').strip(),
            'maker_enabled': maker_enabled,
            'serial_number_range': data.get('serial_number_range', '').strip(),
            'cid_prefix': data.get('cid_prefix', '').strip(),
            'series_name_in_cid': data.get('series_name_in_cid', '').strip(),
            'number_padding': number_padding,
            'suffix': data.get('suffix', '').strip(),
            'priority': max_priority + 1,  # 保持从0开始的连续性
            'description': data.get('description', '').strip()
        }

        # 插入数据
        variant_id = cid_variant_dao.insert(variant_data)

        if variant_id:
            return jsonify({"success": True, "message": "变体规则创建成功", "id": variant_id})
        else:
            return jsonify({"success": False, "message": "变体规则创建失败"}), 500

    except Exception as e:
        logger.error(f"创建变体规则失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"创建变体规则失败: {str(e)}"}), 500

@cid_api.route('/cid/variant-rules/<int:variant_id>', methods=['PUT'])
def update_variant_rule(variant_id):
    """更新变体规则"""
    try:
        data = request.get_json()

        # 验证变体规则是否存在
        existing_variant = cid_variant_dao.find_by_id(variant_id)
        if not existing_variant:
            return jsonify({"success": False, "message": "变体规则不存在"}), 404

        # 准备更新数据
        update_data = {}
        allowed_fields = ['maker', 'maker_enabled', 'serial_number_range', 'cid_prefix', 'series_name_in_cid', 'number_padding', 'suffix', 'description']

        for field in allowed_fields:
            if field in data:
                if field == 'number_padding':
                    update_data[field] = int(data[field])
                elif field == 'maker_enabled':
                    update_data[field] = int(data[field])
                else:
                    update_data[field] = data[field]

        # 更新数据
        success = cid_variant_dao.update(variant_id, update_data)

        if success:
            return jsonify({"success": True, "message": "变体规则更新成功"})
        else:
            return jsonify({"success": False, "message": "变体规则更新失败"}), 500

    except Exception as e:
        logger.error(f"更新变体规则失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"更新变体规则失败: {str(e)}"}), 500

@cid_api.route('/cid/variant-rules/<int:variant_id>', methods=['DELETE'])
def delete_variant_rule(variant_id):
    """删除变体规则"""
    try:
        # 验证变体规则是否存在
        existing_variant = cid_variant_dao.find_by_id(variant_id)
        if not existing_variant:
            return jsonify({"success": False, "message": "变体规则不存在"}), 404

        # 删除变体规则
        success = cid_variant_dao.delete(variant_id)

        if success:
            return jsonify({"success": True, "message": "变体规则删除成功"})
        else:
            return jsonify({"success": False, "message": "变体规则删除失败"}), 500

    except Exception as e:
        logger.error(f"删除变体规则失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"删除变体规则失败: {str(e)}"}), 500

@cid_api.route('/cid/variant-rules/reorder', methods=['PUT'])
def reorder_variant_rules():
    """调整变体规则优先级顺序"""
    try:
        data = request.get_json()
        variant_orders = data.get('variant_orders', [])

        if not variant_orders:
            return jsonify({"success": False, "message": "缺少排序数据"}), 400

        # 批量更新优先级
        success = cid_variant_dao.update_priorities(variant_orders)

        if success:
            return jsonify({"success": True, "message": "变体规则优先级调整成功"})
        else:
            return jsonify({"success": False, "message": "变体规则优先级调整失败"}), 500

    except Exception as e:
        logger.error(f"调整变体规则优先级失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"调整变体规则优先级失败: {str(e)}"}), 500



# ==================== CID生成API ====================

@cid_api.route('/cid/generate', methods=['POST'])
def generate_cid():
    """根据番号和制作商生成CID"""
    try:

        data = request.get_json()
        bangou = data.get('bangou', '').strip()
        maker = data.get('maker', '').strip()

        if not bangou:
            return jsonify({"success": False, "message": "请输入番号"}), 400

        # 使用CID服务生成CID
        result = cid_service.generate_cid(bangou, maker if maker else None)
        return jsonify(result)

    except ValueError as e:
        logger.warning(f"CID生成参数错误: {e}")
        return jsonify({"success": False, "message": str(e)}), 400
    except Exception as e:
        logger.error(f"CID生成失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"CID生成失败: {str(e)}"}), 500

@cid_api.route('/cid/parse-bangou', methods=['POST'])
def parse_bangou():
    """解析番号"""
    try:
        data = request.get_json()
        bangou = data.get('bangou', '').strip()

        if not bangou:
            return jsonify({"success": False, "message": "请输入番号"}), 400

        # 解析番号
        result = cid_service.parse_bangou(bangou)
        return jsonify({"success": True, "data": result})

    except ValueError as e:
        logger.warning(f"番号解析失败: {e}")
        return jsonify({"success": False, "message": str(e)}), 400
    except Exception as e:
        logger.error(f"番号解析失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"番号解析失败: {str(e)}"}), 500

@cid_api.route('/cid/avbase-lookup', methods=['POST'])
def avbase_lookup():
    """AVBase查询CID并推导规则"""
    try:
        data = request.get_json()
        bangou = data.get('bangou', '').strip()
        target_maker = data.get('maker', '').strip()
        target_maker = target_maker if target_maker else None  # 可选的目标制作商
        skip_rule_derivation = data.get('skip_rule_derivation', False)  # 是否跳过规则推导

        if not bangou:
            return jsonify({"success": False, "message": "请输入番号"}), 400

        # 根据是否跳过规则推导选择查询方式
        if skip_rule_derivation:
            # 手动处理页面：使用简单查询，不获取制作商
            correct_cid, maker = scrape_cid_and_maker(bangou)
            return jsonify({
                "success": True,
                "bangou": bangou.upper(),
                "cid": correct_cid
            })
        else:
            # 规则管理页面：使用制作商匹配查询
            if target_maker:
                correct_cid, maker = scrape_cid_and_maker_with_matching(bangou, target_maker)
            else:
                # 规则管理页面即使没有指定制作商，也要获取制作商信息用于显示
                correct_cid, maker = scrape_cid_and_maker_with_matching(bangou, None)

        new_rule = derive_rule_from_cid(bangou, correct_cid)

        # 检查是否已存在主规则
        existing_rule = cid_rule_dao.find_by_series_name(new_rule['series_name'])

        suggestion = "新规则"
        main_rule_id = None

        if existing_rule:
            # 检查推导的规则是否与现有主规则完全匹配
            rule_matches = (
                existing_rule.get('cid_prefix', '') == new_rule.get('prefix', '') and
                existing_rule.get('series_name_in_cid', '') == new_rule.get('series_name_in_cid', '') and
                existing_rule.get('number_padding', 0) == new_rule.get('padding', 0) and
                existing_rule.get('suffix', '') == new_rule.get('suffix', '')
            )

            if rule_matches:
                suggestion = "规则已存在"
            else:
                # 检查是否会产生重复的CID
                from services.cid_service import cid_service
                try:
                    # 使用推导的规则生成CID
                    test_cid = cid_service._generate_cid_with_rule(bangou, new_rule)
                    if test_cid == correct_cid:
                        # 检查数据库中是否已有相同的CID生成规则
                        variants = cid_variant_dao.find_by_main_rule_id(existing_rule['id'])
                        for variant in variants:
                            variant_rule = {
                                'prefix': variant.get('cid_prefix', ''),
                                'series_name_in_cid': variant.get('series_name_in_cid', ''),
                                'padding': variant.get('number_padding', 0),
                                'suffix': variant.get('suffix', '')
                            }
                            variant_cid = cid_service._generate_cid_with_rule(bangou, variant_rule)
                            if variant_cid == correct_cid:
                                suggestion = "CID重复"
                                break
                        else:
                            suggestion = "变体规则"
                            main_rule_id = existing_rule['id']
                    else:
                        suggestion = "变体规则"
                        main_rule_id = existing_rule['id']
                except Exception as e:
                    logger.warning(f"检查CID重复时出错: {e}")
                    suggestion = "变体规则"
                    main_rule_id = existing_rule['id']

        result = {
            "success": True,
            "bangou": bangou.upper(),
            "cid": correct_cid,
            "maker": maker,
            "derived_rule": new_rule,
            "existing_rule": existing_rule,
            "main_rule_id": main_rule_id,
            "suggestion": suggestion
        }

        return jsonify(result)

    except Exception as e:
        logger.error(f"AVBase查询失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"AVBase查询失败: {str(e)}"}), 500

@cid_api.route('/cid/parse-rule', methods=['POST'])
def parse_rule_from_string():
    """从番号,CID字符串解析规则"""
    try:
        data = request.get_json()
        parse_string = data.get('text', '').strip()

        if not parse_string:
            return jsonify({"success": False, "message": "请输入解析文本"}), 400

        parts = parse_string.split(',')
        if len(parts) != 2:
            return jsonify({"success": False, "message": "格式错误，应为 '番号,cid'"}), 400

        bangou, cid = parts[0].strip(), parts[1].strip()

        # 使用现有的derive_rule_from_cid函数
        rule = derive_rule_from_cid(bangou, cid)

        return jsonify({"success": True, "rule": rule})

    except ValueError as e:
        logger.warning(f"规则解析失败: {e}")
        return jsonify({"success": False, "message": str(e)}), 400
    except Exception as e:
        logger.error(f"规则解析失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"规则解析失败: {str(e)}"}), 500

@cid_api.route('/cid/batch-import', methods=['POST'])
def batch_import_rules():
    """批量导入主规则"""
    try:
        data = request.get_json()
        rules_text = data.get('rules_text', '').strip()

        if not rules_text:
            return jsonify({"success": False, "message": "请输入规则数据"}), 400

        # 解析规则文本
        lines = [line.strip() for line in rules_text.split('\n') if line.strip()]
        imported_count = 0
        skipped_count = 0
        errors = []

        # 使用事务进行批量导入，提高性能和数据一致性
        from db_context import db_context

        try:
            with db_context.get_connection(auto_commit=False) as conn:
                for line_num, line in enumerate(lines, 1):
                    try:
                        # 支持的格式：番号前缀,CID前缀,CID系列名,补位规则,后缀,描述
                        # 例如：SW,1,sw,5,-,SWITCH系列
                        parts = [part.strip() for part in line.split(',')]

                        if len(parts) < 4:
                            errors.append(f"第{line_num}行格式错误：至少需要4个字段（番号前缀,CID前缀,CID系列名,补位规则）")
                            continue

                        series_name = parts[0].upper().strip()
                        if not series_name:
                            errors.append(f"第{line_num}行错误：番号前缀不能为空")
                            continue

                        cid_prefix = parts[1].strip() if parts[1] != '-' else ''
                        series_name_in_cid = parts[2].strip()

                        # 解析补位规则
                        try:
                            number_padding = int(parts[3]) if parts[3].isdigit() else 0
                            if number_padding < 0 or number_padding > 10:
                                errors.append(f"第{line_num}行错误：补位规则必须在0-10之间")
                                continue
                        except ValueError:
                            errors.append(f"第{line_num}行错误：补位规则必须是有效数字")
                            continue

                        suffix = parts[4].strip() if len(parts) > 4 and parts[4] != '-' else ''
                        description = parts[5].strip() if len(parts) > 5 else f"{series_name}系列规则"

                        # 检查是否已存在
                        existing = cid_rule_dao.find_by_series_name(series_name)
                        if existing:
                            skipped_count += 1
                            continue

                        # 创建规则
                        rule_data = {
                            'series_name': series_name,
                            'cid_prefix': cid_prefix,
                            'series_name_in_cid': series_name_in_cid,
                            'number_padding': number_padding,
                            'suffix': suffix,
                            'description': description,
                            'status': '已确认'
                        }

                        # 使用事务连接进行插入
                        cid_rule_dao.insert(rule_data, connection=conn)
                        imported_count += 1

                    except Exception as e:
                        errors.append(f"第{line_num}行处理失败：{str(e)}")

                # 提交所有更改
                conn.commit()

        except Exception as e:
            logger.error(f"批量导入事务失败: {e}", exc_info=True)
            errors.append(f"批量导入事务失败: {str(e)}")
            # 事务会自动回滚

        result = {
            "success": True,
            "message": f"批量导入完成：成功导入{imported_count}条，跳过{skipped_count}条",
            "imported_count": imported_count,
            "skipped_count": skipped_count,
            "errors": errors
        }

        return jsonify(result)

    except Exception as e:
        logger.error(f"批量导入失败: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"批量导入失败: {str(e)}"}), 500
