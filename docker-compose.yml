services:
  jassistant-test:
    image: test:2.0.0
    container_name: jassistant-test
    network_mode: bridge
    restart: always
    ports:
      - "34712:34711"
    # 日志轮替设置：限制每个日志文件最大为10MB，最多保留3个日志文件
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    volumes:
      - ./data/logs:/app/logs
      - ./data/db:/app/db
      - ./data/settings:/app/settings
      - ./data/watermarks:/app/assets
      - ./data/cache:/app/data/cache
      - ./data/cover_cache:/app/cover_cache
      - ./data/backups:/app/backups  # 数据库备份存储目录
      - /vol1/1000/weiam:/weiam
    environment:
      - TZ=Asia/Shanghai
      # 设置Python环境变量
      - PYTHONUNBUFFERED=1
