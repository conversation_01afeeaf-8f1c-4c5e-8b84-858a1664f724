#!/bin/bash

echo "CID表修复脚本"
echo "============================================"

# 检查docker-compose是否运行
if docker-compose ps | grep -q "jassistant-test"; then
    echo "检测到容器正在运行，在容器内执行修复..."
    docker-compose exec jassistant-test python /app/backend/fix_cid_tables.py
else
    echo "容器未运行，直接在主机上执行修复..."
    cd backend
    python fix_cid_tables.py
    cd ..
fi

echo ""
echo "修复完成！请重启应用："
echo "docker-compose restart"
