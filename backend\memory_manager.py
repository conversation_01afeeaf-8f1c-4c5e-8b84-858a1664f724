import gc
import psutil
import threading
import time
import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class MemoryManager:
    """内存管理器"""
    
    def __init__(self, 
                 memory_threshold_mb: int = 150,  # 内存阈值150MB
                 cleanup_interval: int = 300,     # 5分钟检查一次
                 auto_cleanup: bool = True):
        
        self.memory_threshold_mb = memory_threshold_mb
        self.cleanup_interval = cleanup_interval
        self.auto_cleanup = auto_cleanup
        self.cleanup_thread = None
        self.running = False
        
        if auto_cleanup:
            self.start_auto_cleanup()
    
    def get_memory_usage(self) -> Dict[str, float]:
        """获取当前内存使用情况"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                'rss_mb': memory_info.rss / 1024 / 1024,      # 物理内存
                'vms_mb': memory_info.vms / 1024 / 1024,      # 虚拟内存
                'percent': process.memory_percent(),           # 内存占用百分比
                'available_mb': psutil.virtual_memory().available / 1024 / 1024
            }
        except Exception as e:
            logger.error(f"获取内存使用情况失败: {e}")
            return {}
    
    def should_cleanup(self) -> bool:
        """判断是否需要清理内存"""
        memory_usage = self.get_memory_usage()
        current_mb = memory_usage.get('rss_mb', 0)
        
        return current_mb > self.memory_threshold_mb
    
    def cleanup_memory(self) -> Dict[str, Any]:
        """清理内存"""
        before_memory = self.get_memory_usage()
        
        try:
            # 清理缓存
            from cache_manager import cache_manager
            cache_result = cache_manager.force_memory_cleanup()
            
            # 强制垃圾回收
            collected = gc.collect()
            
            # 获取清理后的内存使用情况
            after_memory = self.get_memory_usage()
            
            freed_mb = before_memory.get('rss_mb', 0) - after_memory.get('rss_mb', 0)
            
            result = {
                'success': True,
                'before_memory_mb': before_memory.get('rss_mb', 0),
                'after_memory_mb': after_memory.get('rss_mb', 0),
                'freed_mb': freed_mb,
                'gc_collected': collected,
                'cache_cleared': cache_result
            }
            
            logger.info(f"内存清理完成 - 释放: {freed_mb:.1f}MB, GC回收: {collected}")
            return result
            
        except Exception as e:
            logger.error(f"内存清理失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'before_memory_mb': before_memory.get('rss_mb', 0)
            }
    
    def auto_cleanup_worker(self):
        """自动清理工作线程"""
        while self.running:
            try:
                if self.should_cleanup():
                    logger.info(f"内存使用超过阈值 {self.memory_threshold_mb}MB，开始自动清理")
                    self.cleanup_memory()
                
                time.sleep(self.cleanup_interval)
                
            except Exception as e:
                logger.error(f"自动内存清理出错: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续
    
    def start_auto_cleanup(self):
        """启动自动清理"""
        if self.running:
            return
        
        self.running = True
        self.cleanup_thread = threading.Thread(target=self.auto_cleanup_worker, daemon=True)
        self.cleanup_thread.start()
        logger.info(f"内存自动清理已启动 - 阈值: {self.memory_threshold_mb}MB, 间隔: {self.cleanup_interval}s")
    
    def stop_auto_cleanup(self):
        """停止自动清理"""
        self.running = False
        if self.cleanup_thread:
            self.cleanup_thread.join(timeout=5)
        logger.info("内存自动清理已停止")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取内存管理统计信息"""
        memory_usage = self.get_memory_usage()
        
        return {
            'current_memory': memory_usage,
            'threshold_mb': self.memory_threshold_mb,
            'auto_cleanup_enabled': self.auto_cleanup,
            'cleanup_interval': self.cleanup_interval,
            'should_cleanup': self.should_cleanup()
        }

# 全局内存管理器实例
memory_manager = None

def get_memory_manager() -> MemoryManager:
    """获取内存管理器实例"""
    global memory_manager
    if memory_manager is None:
        try:
            from config_utils import get_settings
            settings = get_settings()
            
            # 从配置中读取内存管理参数
            memory_threshold = settings.get('memory_threshold_mb', 150)
            cleanup_interval = settings.get('memory_cleanup_interval', 300)
            auto_cleanup = settings.get('enable_auto_memory_cleanup', True)
            
            memory_manager = MemoryManager(
                memory_threshold_mb=memory_threshold,
                cleanup_interval=cleanup_interval,
                auto_cleanup=auto_cleanup
            )
        except Exception as e:
            logger.warning(f"创建内存管理器失败，使用默认配置: {e}")
            memory_manager = MemoryManager()
    
    return memory_manager
