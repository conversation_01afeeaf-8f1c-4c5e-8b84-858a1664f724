#!/usr/bin/env python3
"""
数据库迁移脚本：将 studio 字段统一改为 maker 字段
"""

import sqlite3
import os
import sys
import logging
from datetime import datetime

# 添加父目录到路径，以便导入项目模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db_manager import get_db_connection

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration_studio_to_maker.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def migrate_studio_to_maker():
    """执行从 studio 到 maker 的数据库迁移"""
    
    logger.info("开始执行 studio 到 maker 的数据库迁移...")
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    try:
        # 开始事务
        cursor.execute("BEGIN TRANSACTION")
        
        # 1. 检查是否已经存在 makers 表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='makers'")
        makers_exists = cursor.fetchone() is not None
        
        if not makers_exists:
            logger.info("创建 makers 表...")
            # 创建 makers 表
            cursor.execute("""
                CREATE TABLE makers (
                    id INTEGER PRIMARY KEY, 
                    name TEXT UNIQUE NOT NULL
                )
            """)
            
            # 创建 nfo_makers 关联表
            cursor.execute("""
                CREATE TABLE nfo_makers (
                    nfo_id INTEGER, 
                    maker_id INTEGER, 
                    FOREIGN KEY(nfo_id) REFERENCES nfo_data(id) ON DELETE CASCADE, 
                    FOREIGN KEY(maker_id) REFERENCES makers(id) ON DELETE CASCADE, 
                    PRIMARY KEY (nfo_id, maker_id)
                )
            """)
            
            logger.info("makers 表和 nfo_makers 关联表创建完成")
        else:
            logger.info("makers 表已存在，跳过创建")
        
        # 2. 检查是否存在 studios 表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='studios'")
        studios_exists = cursor.fetchone() is not None
        
        if studios_exists:
            logger.info("开始迁移 studios 表数据到 makers 表...")
            
            # 将 studios 表的数据迁移到 makers 表
            cursor.execute("SELECT id, name FROM studios")
            studios_data = cursor.fetchall()
            
            for studio_id, studio_name in studios_data:
                # 检查 makers 表中是否已存在相同名称
                cursor.execute("SELECT id FROM makers WHERE name = ?", (studio_name,))
                existing_maker = cursor.fetchone()
                
                if existing_maker:
                    maker_id = existing_maker[0]
                    logger.info(f"制作商 '{studio_name}' 已存在于 makers 表中，使用现有ID: {maker_id}")
                else:
                    # 插入到 makers 表
                    cursor.execute("INSERT INTO makers (name) VALUES (?)", (studio_name,))
                    maker_id = cursor.lastrowid
                    logger.info(f"迁移制作商: '{studio_name}' -> makers 表 ID: {maker_id}")
                
                # 迁移关联关系
                cursor.execute("SELECT nfo_id FROM nfo_studios WHERE studio_id = ?", (studio_id,))
                nfo_relations = cursor.fetchall()
                
                for (nfo_id,) in nfo_relations:
                    # 检查是否已存在相同的关联关系
                    cursor.execute("SELECT 1 FROM nfo_makers WHERE nfo_id = ? AND maker_id = ?", (nfo_id, maker_id))
                    if not cursor.fetchone():
                        cursor.execute("INSERT INTO nfo_makers (nfo_id, maker_id) VALUES (?, ?)", (nfo_id, maker_id))
                        logger.debug(f"迁移关联关系: nfo_id={nfo_id}, maker_id={maker_id}")
            
            logger.info(f"成功迁移 {len(studios_data)} 个制作商数据")
            
            # 3. 删除旧的 studios 相关表
            logger.info("删除旧的 studios 相关表...")
            cursor.execute("DROP TABLE IF EXISTS nfo_studios")
            cursor.execute("DROP TABLE IF EXISTS studios")
            logger.info("旧表删除完成")
        else:
            logger.info("studios 表不存在，跳过数据迁移")
        
        # 4. 创建索引
        logger.info("创建索引...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_makers_name ON makers(name)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_nfo_makers_nfo_id ON nfo_makers(nfo_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_nfo_makers_maker_id ON nfo_makers(maker_id)")
        
        # 提交事务
        conn.commit()
        logger.info("数据库迁移完成！")
        
        # 5. 验证迁移结果
        cursor.execute("SELECT COUNT(*) FROM makers")
        makers_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM nfo_makers")
        relations_count = cursor.fetchone()[0]
        
        logger.info(f"迁移结果验证: makers 表有 {makers_count} 条记录，nfo_makers 关联表有 {relations_count} 条记录")
        
        return True
        
    except Exception as e:
        # 回滚事务
        conn.rollback()
        logger.error(f"迁移过程中发生错误: {e}")
        return False
        
    finally:
        conn.close()

def main():
    """主函数"""
    print("=" * 60)
    print("数据库迁移工具：Studio -> Maker")
    print("=" * 60)
    
    # 确认执行
    response = input("是否确认执行数据库迁移？这将修改数据库结构。(y/N): ")
    if response.lower() != 'y':
        print("迁移已取消")
        return
    
    # 执行迁移
    success = migrate_studio_to_maker()
    
    if success:
        print("\n✅ 迁移成功完成！")
        print("现在可以重启应用程序以使用新的 maker 字段。")
    else:
        print("\n❌ 迁移失败！请检查日志文件了解详细错误信息。")
        sys.exit(1)

if __name__ == "__main__":
    main()
